# QGIS 集成功能说明

## 功能概述

地理编辑面板现在包含一个"高级编辑"按钮，可以自动启动用户本机安装的 QGIS 程序，实现更高级的地理数据编辑功能。

## 功能特性

- ✅ 自动检测运行环境（浏览器 vs Electron）
- ✅ 支持多种 QGIS 启动方式
- ✅ 跨平台支持（Windows、macOS、Linux）
- ✅ 智能错误处理和用户指导
- ✅ 安装状态检查
- ✅ 详细的安装指南

## 使用方法

### 在浏览器环境中

1. 确保已安装 QGIS 软件
2. 点击地理编辑面板底部的"高级编辑"按钮
3. 系统会尝试使用协议启动 QGIS
4. 如果启动失败，会显示安装指南

### 在 Electron 环境中

1. 集成 `electron-qgis-example.js` 到主进程
2. 注册 IPC 处理器
3. 点击"高级编辑"按钮即可启动 QGIS

## 技术实现

### 文件结构

```
frontend/src/utils/
├── qgisLauncher.ts          # QGIS 启动器核心逻辑
├── electron-qgis-example.js # Electron 主进程示例
└── QGIS_INTEGRATION_README.md # 说明文档
```

### 核心组件

#### 1. qgisLauncher.ts
- `launchQGIS()`: 主启动函数
- `launchQGISViaElectron()`: Electron 环境启动
- `launchQGISViaBrowser()`: 浏览器环境启动
- `checkQGISInstallation()`: 安装状态检查
- `getQGISInstallGuide()`: 获取安装指南

#### 2. GeoEditPanel.vue
- 添加了"高级编辑"按钮
- 集成启动逻辑和错误处理
- 用户友好的反馈界面

## 支持的启动方式

### 浏览器环境
- `qgis://` 协议
- `qgis-desktop://` 协议
- `qgis3://` 协议

### Electron 环境
- Windows: 直接执行 qgis.exe
- macOS: 使用 `open` 命令启动 .app
- Linux: 执行 qgis 命令或 Flatpak

## QGIS 安装要求

### Windows
- QGIS 3.x 版本
- 推荐安装路径：`C:\Program Files\QGIS 3.x\`
- 确保协议关联正确

### macOS
- QGIS 3.x 版本
- 安装在 `/Applications/` 目录
- 首次运行需要在安全设置中允许

### Linux
- 通过包管理器安装：`sudo apt install qgis`
- 或使用 Flatpak：`flatpak install qgis`
- 确保 qgis 命令在 PATH 中

## Electron 集成步骤

### 1. 主进程集成

```javascript
// main.js
const { registerQGISHandlers } = require('./utils/electron-qgis-example')

// 注册 QGIS 相关的 IPC 处理器
registerQGISHandlers()
```

### 2. 预加载脚本

```javascript
// preload.js
const { contextBridge, ipcRenderer } = require('electron')

contextBridge.exposeInMainWorld('electronAPI', {
  openQGIS: () => ipcRenderer.invoke('open-qgis'),
  checkQGISInstallation: () => ipcRenderer.invoke('check-qgis-installation')
})
```

### 3. 类型声明

已在 `custom-libs.d.ts` 中添加了相关类型声明。

## 故障排除

### 常见问题

1. **协议启动失败**
   - 检查 QGIS 是否正确安装
   - 确认协议关联是否正确
   - 尝试手动启动 QGIS

2. **权限问题**
   - Windows: 以管理员身份运行浏览器
   - macOS: 在安全设置中允许应用运行
   - Linux: 检查文件权限

3. **路径问题**
   - 确保 QGIS 安装在标准路径
   - 检查环境变量设置
   - 验证可执行文件存在

### 调试信息

启动过程会在控制台输出详细的调试信息：
- 🗺️ 启动尝试
- 🖥️ Electron 环境检测
- 🌐 浏览器环境检测
- 🔗 协议尝试
- ✅ 启动成功
- ❌ 启动失败

## 扩展功能

### 未来可能的增强

1. **数据传递**
   - 将当前编辑的数据传递给 QGIS
   - 支持临时文件创建和清理

2. **双向通信**
   - 监听 QGIS 的数据变更
   - 自动同步编辑结果

3. **插件集成**
   - 开发专用的 QGIS 插件
   - 实现更深度的集成

4. **配置管理**
   - 用户自定义 QGIS 路径
   - 启动参数配置

## 安全考虑

- 协议启动是安全的，不会执行任意代码
- Electron 环境中会验证可执行文件路径
- 不会传递敏感数据给外部程序

## 兼容性

- **浏览器**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **QGIS**: 3.0+ (推荐 3.28 LTR 或更高版本)
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+

## 许可证

此功能遵循项目的整体许可证协议。QGIS 是开源软件，遵循 GPL v2 许可证。
