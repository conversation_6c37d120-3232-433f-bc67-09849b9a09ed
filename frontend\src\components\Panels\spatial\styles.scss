.spatial-panel {
  padding: 1rem;

  .panel-section {
    margin-bottom: 2rem;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      h3 {
        margin: 0;
        font-size: 0.9rem;
        color: #333;
      }
    }
  }

  .analysis-tools {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;

    .tool-card {
      padding: 1rem;
      background: white;
      border: 1px solid #ddd;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &.active {
        border-color: #1890ff;
        background: #e6f7ff;
      }

      .tool-icon {
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
      }

      .tool-name {
        font-weight: 500;
        color: #333;
        font-size: 0.875rem;
      }

      .tool-desc {
        font-size: 0.75rem;
        color: #666;
        margin-top: 0.5rem;
      }
    }
  }

  .analysis-params {
    margin-top: 1.5rem;
    padding: 1rem;
    background: #f5f5f5;
    border-radius: 8px;

    .param-group {
      margin-bottom: 1rem;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        display: block;
        margin-bottom: 0.5rem;
        color: #666;
        font-size: 0.75rem;
      }

      .el-select,
      .el-input-number {
        width: 100%;
      }
    }
  }

  .analysis-actions {
    margin-top: 1rem;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;

    .el-button {
      min-width: 100px;
    }
  }

  .results-section {
    margin-top: 2rem;

    .result-card {
      padding: 1rem;
      background: white;
      border: 1px solid #ddd;
      border-radius: 8px;
      margin-bottom: 1rem;

      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        .result-type {
          font-weight: 500;
          color: #333;
          font-size: 0.875rem;
        }

        .result-time {
          font-size: 0.75rem;
          color: #999;
        }
      }

      .result-content {
        margin-top: 1rem;
        padding: 1rem;
        background: #f5f5f5;
        border-radius: 4px;
        font-family: monospace;
        font-size: 0.75rem;
      }
    }

    .no-results {
      text-align: center;
      padding: 2rem;
      color: #999;
      font-style: italic;
      font-size: 0.75rem;
    }
  }
}

// 动画效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(20px);
  opacity: 0;
}