"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const typeorm_1 = require("typeorm");
const bcrypt = require("bcryptjs");
const role_entity_1 = require("./role.entity");
const user_session_entity_1 = require("./user-session.entity");
const audit_log_entity_1 = require("./audit-log.entity");
let User = class User {
    user_id;
    username;
    email;
    password_hash;
    full_name;
    department;
    phone;
    is_active;
    is_first_login;
    last_login;
    login_attempts;
    locked_until;
    password_changed_at;
    created_at;
    updated_at;
    created_by;
    roles;
    sessions;
    audit_logs;
    password;
    get id() {
        return this.user_id;
    }
    get fullName() {
        return this.full_name || '';
    }
    set fullName(value) {
        this.full_name = value;
    }
    get isActive() {
        return this.is_active;
    }
    set isActive(value) {
        this.is_active = value;
    }
    get isFirstLogin() {
        return this.is_first_login;
    }
    set isFirstLogin(value) {
        this.is_first_login = value;
    }
    get lastLoginAt() {
        return this.last_login;
    }
    set lastLoginAt(value) {
        this.last_login = value;
    }
    get failedLoginAttempts() {
        return this.login_attempts;
    }
    set failedLoginAttempts(value) {
        this.login_attempts = value;
    }
    get lockedAt() {
        return this.locked_until;
    }
    set lockedAt(value) {
        this.locked_until = value;
    }
    get lockExpiresAt() {
        return this.locked_until;
    }
    set lockExpiresAt(value) {
        this.locked_until = value;
    }
    get passwordHash() {
        return this.password_hash;
    }
    set passwordHash(value) {
        this.password_hash = value;
    }
    get passwordChangedAt() {
        return this.password_changed_at;
    }
    set passwordChangedAt(value) {
        this.password_changed_at = value;
    }
    get createdAt() {
        return this.created_at;
    }
    get updatedAt() {
        return this.updated_at;
    }
    get isLocked() {
        return !!(this.locked_until && new Date() < this.locked_until);
    }
    async hashPassword() {
        if (this.password) {
            const rounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
            this.password_hash = await bcrypt.hash(this.password, rounds);
            delete this.password;
        }
    }
    async validatePassword(password) {
        return bcrypt.compare(password, this.password_hash);
    }
    async incrementLoginAttempts() {
        this.login_attempts += 1;
        const maxAttempts = parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5');
        if (this.login_attempts >= maxAttempts) {
            const lockTimeMinutes = parseInt(process.env.LOCK_TIME?.replace('m', '') || '15');
            this.locked_until = new Date(Date.now() + lockTimeMinutes * 60 * 1000);
        }
    }
    resetLoginAttempts() {
        this.login_attempts = 0;
        this.locked_until = null;
    }
    updateLastLogin() {
        this.last_login = new Date();
        this.resetLoginAttempts();
    }
    getPermissions() {
        if (!this.roles)
            return [];
        const permissions = new Set();
        for (const role of this.roles) {
            const rolePermissions = role.getPermissions();
            rolePermissions.forEach(permission => permissions.add(permission));
        }
        return Array.from(permissions);
    }
    hasPermission(permission) {
        const userPermissions = this.getPermissions();
        if (userPermissions.includes('*')) {
            return true;
        }
        return userPermissions.includes(permission);
    }
    hasRole(roleName) {
        if (!this.roles)
            return false;
        return this.roles.some(role => role.name === roleName);
    }
    toSafeObject() {
        const { password_hash, password, login_attempts, locked_until, ...safeUser } = this;
        return {
            ...safeUser,
            id: this.id,
            fullName: this.fullName,
            isActive: this.isActive,
            isFirstLogin: this.isFirstLogin,
            lastLoginAt: this.lastLoginAt,
            failedLoginAttempts: this.failedLoginAttempts,
            isLocked: this.isLocked,
            passwordChangedAt: this.passwordChangedAt,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            roles: this.roles?.map(role => ({
                id: role.role_id,
                name: role.name,
                displayName: role.display_name,
                description: role.description,
            })),
            permissions: this.getPermissions(),
        };
    }
};
exports.User = User;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], User.prototype, "user_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], User.prototype, "username", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, unique: true }),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], User.prototype, "password_hash", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "full_name", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "department", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 20, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "phone", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], User.prototype, "is_active", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], User.prototype, "is_first_login", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamptz', nullable: true }),
    __metadata("design:type", Date)
], User.prototype, "last_login", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], User.prototype, "login_attempts", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamptz', nullable: true }),
    __metadata("design:type", Object)
], User.prototype, "locked_until", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamptz', nullable: true }),
    __metadata("design:type", Object)
], User.prototype, "password_changed_at", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamptz' }),
    __metadata("design:type", Date)
], User.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ type: 'timestamptz' }),
    __metadata("design:type", Date)
], User.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], User.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => role_entity_1.Role, role => role.users),
    (0, typeorm_1.JoinTable)({
        name: 'user_roles',
        schema: 'system',
        joinColumn: { name: 'user_id', referencedColumnName: 'user_id' },
        inverseJoinColumn: { name: 'role_id', referencedColumnName: 'role_id' },
    }),
    __metadata("design:type", Array)
], User.prototype, "roles", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => user_session_entity_1.UserSession, session => session.user),
    __metadata("design:type", Array)
], User.prototype, "sessions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => audit_log_entity_1.AuditLog, log => log.user),
    __metadata("design:type", Array)
], User.prototype, "audit_logs", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    (0, typeorm_1.BeforeUpdate)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], User.prototype, "hashPassword", null);
exports.User = User = __decorate([
    (0, typeorm_1.Entity)('users', { schema: 'system' })
], User);
//# sourceMappingURL=user.entity.js.map