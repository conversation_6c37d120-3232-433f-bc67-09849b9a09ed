export declare class LoginDto {
    username: string;
    password: string;
    captcha?: string;
}
export declare class LoginResponseDto {
    access_token: string;
    refresh_token: string;
    user: {
        user_id: string;
        username: string;
        email: string;
        full_name: string;
        roles: string[];
        permissions: string[];
    };
    expires_in: number;
}
export declare class RefreshTokenDto {
    refreshToken?: string;
}
export declare class ChangePasswordDto {
    currentPassword: string;
    newPassword: string;
}
export declare class SendEmailCodeDto {
    email: string;
    type?: 'login' | 'register' | 'reset';
}
export declare class EmailLoginDto {
    email: string;
    code: string;
}
export declare class FirstLoginDto {
    password: string;
    full_name?: string;
    phone?: string;
}
export declare class RegisterDto {
    username: string;
    email: string;
    emailCode: string;
    password: string;
    confirmPassword: string;
    fullName?: string;
    phone?: string;
}
export declare class RegisterResponseDto {
    message: string;
    user: {
        id: string;
        username: string;
        email: string;
        fullName: string;
    };
}
