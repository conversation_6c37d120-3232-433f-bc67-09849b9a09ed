"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuthController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const auth_service_1 = require("./auth.service");
const local_auth_guard_1 = require("./guards/local-auth.guard");
const jwt_auth_guard_1 = require("./guards/jwt-auth.guard");
const public_decorator_1 = require("./decorators/public.decorator");
const get_user_decorator_1 = require("./decorators/get-user.decorator");
const user_entity_1 = require("../entities/user.entity");
const login_dto_1 = require("./dto/login.dto");
let AuthController = AuthController_1 = class AuthController {
    authService;
    logger = new common_1.Logger(AuthController_1.name);
    constructor(authService) {
        this.authService = authService;
    }
    async login(loginDto, req, res) {
        this.logger.log(`=== LOGIN REQUEST RECEIVED === Username: ${loginDto.username}`);
        try {
            const userAgent = req.get('user-agent');
            const ipAddress = this.getClientIpAddress(req);
            const loginResult = await this.authService.login(loginDto, userAgent, ipAddress);
            res.cookie('refreshToken', loginResult.refreshToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict',
                maxAge: 7 * 24 * 60 * 60 * 1000,
            });
            this.logger.log(`User ${loginDto.username} logged in successfully`);
            return {
                message: '登录成功',
                data: loginResult,
            };
        }
        catch (error) {
            this.logger.error(`Login failed for user ${loginDto.username}: ${error.message}`);
            throw error;
        }
    }
    async refreshToken(refreshTokenDto, req) {
        try {
            const refreshToken = refreshTokenDto.refreshToken || req.cookies?.refreshToken;
            if (!refreshToken) {
                throw new common_1.UnauthorizedException('刷新令牌缺失');
            }
            const result = await this.authService.refreshToken(refreshToken);
            return {
                message: '令牌刷新成功',
                data: result,
            };
        }
        catch (error) {
            this.logger.error(`Token refresh failed: ${error.message}`);
            throw error;
        }
    }
    async logout(req, res, user) {
        try {
            const refreshToken = req.cookies?.refreshToken;
            if (refreshToken) {
                await this.authService.logout(refreshToken);
            }
            res.clearCookie('refreshToken');
            this.logger.log(`User ${user.username} logged out successfully`);
            return {
                message: '登出成功',
            };
        }
        catch (error) {
            this.logger.error(`Logout failed for user ${user.username}: ${error.message}`);
            throw error;
        }
    }
    async logoutAllDevices(user, res) {
        try {
            await this.authService.logoutAllDevices(user.id);
            res.clearCookie('refreshToken');
            this.logger.log(`User ${user.username} logged out from all devices`);
            return {
                message: '已从所有设备登出',
            };
        }
        catch (error) {
            this.logger.error(`Logout all devices failed for user ${user.username}: ${error.message}`);
            throw error;
        }
    }
    async changePassword(changePasswordDto, user, res) {
        try {
            await this.authService.changePassword(user.id, changePasswordDto);
            res.clearCookie('refreshToken');
            this.logger.log(`Password changed for user ${user.username}`);
            return {
                message: '密码修改成功，请重新登录',
            };
        }
        catch (error) {
            this.logger.error(`Password change failed for user ${user.username}: ${error.message}`);
            throw error;
        }
    }
    async getCurrentUser(user) {
        const permissions = this.getUserPermissions(user);
        return {
            message: '获取用户信息成功',
            data: {
                id: user.id,
                username: user.username,
                email: user.email,
                fullName: user.fullName,
                roles: user.roles.map(role => role.name),
                permissions,
                isActive: user.isActive,
                lastLoginAt: user.lastLoginAt,
                createdAt: user.createdAt,
            },
        };
    }
    async verifyToken(user) {
        return {
            message: '令牌验证成功',
            data: {
                valid: true,
                user: {
                    id: user.id,
                    username: user.username,
                    roles: user.roles.map(role => role.name),
                },
            },
        };
    }
    async resetPassword(username, body) {
        try {
            const result = await this.authService.resetPassword(username, body.password);
            return {
                message: '密码重置成功',
                data: result,
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`密码重置失败: ${error.message}`);
        }
    }
    async register(registerDto) {
        try {
            const result = await this.authService.register(registerDto);
            this.logger.log(`User ${registerDto.username} registered successfully`);
            return {
                message: result.message,
                data: result.user,
            };
        }
        catch (error) {
            this.logger.error(`Registration failed for user ${registerDto.username}: ${error.message}`);
            throw error;
        }
    }
    async sendEmailCode(sendEmailCodeDto) {
        try {
            const { email, type } = sendEmailCodeDto;
            try {
                const result = await this.authService.sendEmailCode(email, type);
                this.logger.log(`Email verification code sent to: ${email}`);
                return {
                    message: result.message,
                };
            }
            catch (mailError) {
                if (mailError.message && mailError.message.includes('Authentication failure')) {
                    this.logger.log(`Mail sending failed but verification code generated for: ${email}`);
                    return {
                        message: '验证码已生成，请查看后台日志获取验证码',
                    };
                }
                throw mailError;
            }
        }
        catch (error) {
            this.logger.error(`Failed to send email code to ${sendEmailCodeDto.email}: ${error.message}`);
            throw error;
        }
    }
    async loginWithEmail(emailLoginDto, req, res) {
        try {
            const userAgent = req.get('user-agent');
            const ipAddress = this.getClientIpAddress(req);
            const loginResult = await this.authService.loginWithEmail(emailLoginDto);
            res.cookie('refreshToken', loginResult.refreshToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict',
                maxAge: 7 * 24 * 60 * 60 * 1000,
            });
            this.logger.log(`User logged in with email: ${emailLoginDto.email}`);
            return {
                message: '邮箱验证码登录成功',
                data: loginResult,
            };
        }
        catch (error) {
            this.logger.error(`Email login failed for ${emailLoginDto.email}: ${error.message}`);
            throw error;
        }
    }
    getClientIpAddress(req) {
        return (req.headers['x-forwarded-for']?.split(',')[0] ||
            req.headers['x-real-ip'] ||
            req.connection.remoteAddress ||
            req.socket.remoteAddress ||
            req.ip ||
            'unknown');
    }
    getUserPermissions(user) {
        const permissions = new Set();
        if (user.roles && Array.isArray(user.roles)) {
            user.roles.forEach(role => {
                const rolePermissions = role.getPermissions();
                rolePermissions.forEach(permission => {
                    permissions.add(permission);
                });
            });
        }
        return Array.from(permissions);
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.UseGuards)(local_auth_guard_1.LocalAuthGuard),
    (0, common_1.Post)('login'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Res)({ passthrough: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [login_dto_1.LoginDto, Object, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('refresh'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [login_dto_1.RefreshTokenDto, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "refreshToken", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('logout'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)({ passthrough: true })),
    __param(2, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, user_entity_1.User]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "logout", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('logout-all'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Res)({ passthrough: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "logoutAllDevices", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('change-password'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)()),
    __param(2, (0, common_1.Res)({ passthrough: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [login_dto_1.ChangePasswordDto,
        user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "changePassword", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('me'),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getCurrentUser", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('verify'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "verifyToken", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('reset-password/:username'),
    __param(0, (0, common_1.Param)('username')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "resetPassword", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('register'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [login_dto_1.RegisterDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "register", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('send-email-code'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [login_dto_1.SendEmailCodeDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "sendEmailCode", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('login-email'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Res)({ passthrough: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [login_dto_1.EmailLoginDto, Object, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "loginWithEmail", null);
exports.AuthController = AuthController = AuthController_1 = __decorate([
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map