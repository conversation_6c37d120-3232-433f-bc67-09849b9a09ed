import { User } from './user.entity';
export declare class UserSession {
    session_id: string;
    user_id: string;
    refresh_token: string;
    ip_address: string;
    user_agent: string;
    expires_at: Date;
    created_at: Date;
    last_used_at: Date;
    user: User;
    password?: string;
    get id(): string;
    get userId(): string;
    set userId(value: string);
    get refreshToken(): string;
    set refreshToken(value: string);
    get ipAddress(): string;
    set ipAddress(value: string);
    get userAgent(): string;
    set userAgent(value: string);
    get expiresAt(): Date;
    set expiresAt(value: Date);
    get createdAt(): Date;
    get lastUsedAt(): Date;
    set lastUsedAt(value: Date);
    get isActive(): boolean;
    get loggedOutAt(): Date | null;
    isExpired(): boolean;
    updateLastUsed(): void;
    toSafeObject(): {
        session_id: string;
        user_id: string;
        ip_address: string;
        user_agent: string;
        expires_at: Date;
        created_at: Date;
        last_used_at: Date;
        is_expired: boolean;
    };
}
