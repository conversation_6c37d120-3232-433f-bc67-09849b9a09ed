import { RedisService } from '@liaoliaots/nestjs-redis';
export declare class MailService {
    private readonly redisService;
    private readonly logger;
    private transporter;
    private redis;
    constructor(redisService: RedisService);
    private testRedisConnection;
    private testSMTPConnection;
    private generateVerificationCode;
    sendVerificationCode(email: string, type?: 'login' | 'register' | 'reset'): Promise<string>;
    verifyCode(email: string, code: string, type?: 'login' | 'register' | 'reset'): Promise<boolean>;
    private getEmailTemplate;
}
