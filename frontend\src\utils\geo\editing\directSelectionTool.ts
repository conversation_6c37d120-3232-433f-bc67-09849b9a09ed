import * as Cesium from "cesium"
import type { GeoFeature, FeatureType, GeoPoint } from '../core/types'
import { geoEventBus, GEO_EVENTS } from '../core/eventBus'

/**
 * 直接选择工具 - 使用最简单直接的方式实现选择功能
 */
export class DirectSelectionTool {
  private viewer: Cesium.Viewer
  private isActive = false
  private selectedFeatures: GeoFeature[] = []
  
  constructor(viewer: Cesium.Viewer) {
    this.viewer = viewer
  }

  /**
   * 激活选择工具
   */
  activate() {
    console.log('🎯 激活直接选择工具')

    this.isActive = true

    // 精细化相机控制：保持缩放，禁用平移和旋转
    const controller = this.viewer.scene.screenSpaceCameraController
    controller.enableRotate = false
    controller.enableTranslate = false
    controller.enableZoom = true  // 保持缩放功能
    controller.enableTilt = false
    controller.enableLook = false

    // 设置点击事件处理
    this.viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(
      (event: Cesium.ScreenSpaceEventHandler.PositionedEvent) => {
        if (!this.isActive) return

        console.log('🎯 点击事件触发')
        this.handleClick(event.position)
      },
      Cesium.ScreenSpaceEventType.LEFT_CLICK
    )

    // 改变鼠标样式
    this.viewer.canvas.style.cursor = 'crosshair'

    console.log('✅ 直接选择工具已激活，缩放功能保持可用')
  }

  /**
   * 停用选择工具
   */
  deactivate() {
    console.log('🎯 停用直接选择工具')

    this.isActive = false

    // 恢复相机控制
    const controller = this.viewer.scene.screenSpaceCameraController
    controller.enableRotate = true
    controller.enableTranslate = true
    controller.enableZoom = true
    controller.enableTilt = true
    controller.enableLook = true

    // 恢复默认事件处理
    this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK)

    // 恢复鼠标样式
    this.viewer.canvas.style.cursor = 'default'

    // 清除选择
    this.clearSelection()

    console.log('✅ 直接选择工具已停用，相机控制已恢复')
  }

  /**
   * 处理点击事件
   */
  private handleClick(position: Cesium.Cartesian2) {
    console.log('🎯 处理点击选择，位置:', position)
    
    // 执行拾取
    const pickedObject = this.viewer.scene.pick(position)
    
    console.log('🎯 拾取结果:', pickedObject)
    
    if (Cesium.defined(pickedObject) && pickedObject.id) {
      const entity = pickedObject.id as Cesium.Entity
      console.log('✅ 选中实体:', entity.id, entity.name)
      
      // 转换为 GeoFeature
      const feature = this.convertEntityToGeoFeature(entity)
      if (feature) {
        this.selectFeature(feature)
      }
    } else {
      console.log('❌ 未选中任何实体')
      this.clearSelection()
    }
  }

  /**
   * 转换实体为地理要素
   */
  private convertEntityToGeoFeature(entity: Cesium.Entity): GeoFeature | null {
    try {
      let featureType: FeatureType = 'point'
      
      if (entity.point) {
        featureType = 'point'
      } else if (entity.polyline) {
        featureType = 'line'
      } else if (entity.polygon) {
        featureType = 'polygon'
      }      const feature: GeoFeature = {
        type: featureType,
        id: entity.id || `feature_${Date.now()}`,
        points: this.extractPoints(entity),
        timestamp: new Date(),
        entity: entity,
        properties: {
          name: entity.name || `未命名${featureType}`,
          description: entity.description?.getValue(Cesium.JulianDate.now()) || '',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      }

      console.log('✅ 实体转换完成:', feature)
      return feature
    } catch (error) {
      console.error('❌ 实体转换失败:', error)
      return null
    }
  }

  /**
   * 提取实体坐标点
   */
  private extractPoints(entity: Cesium.Entity): GeoPoint[] {
    const points: GeoPoint[] = []

    try {
      if (entity.position) {
        const position = entity.position.getValue(Cesium.JulianDate.now())
        if (position) {
          const cartographic = Cesium.Cartographic.fromCartesian(position)
          points.push({
            longitude: Cesium.Math.toDegrees(cartographic.longitude),
            latitude: Cesium.Math.toDegrees(cartographic.latitude),
            height: cartographic.height
          })
        }
      }
      
      if (entity.polyline?.positions) {
        const positions = entity.polyline.positions.getValue(Cesium.JulianDate.now())
        if (positions) {
          positions.forEach((pos: Cesium.Cartesian3) => {
            const cartographic = Cesium.Cartographic.fromCartesian(pos)
            points.push({
              longitude: Cesium.Math.toDegrees(cartographic.longitude),
              latitude: Cesium.Math.toDegrees(cartographic.latitude),
              height: cartographic.height
            })
          })
        }
      }
      
      if (entity.polygon?.hierarchy) {
        const hierarchy = entity.polygon.hierarchy.getValue(Cesium.JulianDate.now())
        if (hierarchy) {
          const positions = Array.isArray(hierarchy) ? hierarchy : hierarchy.positions
          if (positions) {
            positions.forEach((pos: Cesium.Cartesian3) => {
              const cartographic = Cesium.Cartographic.fromCartesian(pos)
              points.push({
                longitude: Cesium.Math.toDegrees(cartographic.longitude),
                latitude: Cesium.Math.toDegrees(cartographic.latitude),
                height: cartographic.height
              })
            })
          }
        }
      }
    } catch (error) {
      console.warn('提取坐标点时出错:', error)
    }

    return points
  }

  /**
   * 选择要素
   */
  private selectFeature(feature: GeoFeature) {
    console.log('✨ 选择要素:', feature)
    
    // 清除之前的选择
    this.clearSelection()
    
    // 添加到选择列表
    this.selectedFeatures.push(feature)
    
    // 创建高亮效果
    this.createHighlight(feature)
    
    // 触发事件
    geoEventBus.emit(GEO_EVENTS.FEATURE_SELECTED, feature)
    geoEventBus.emit(GEO_EVENTS.SELECTION_CHANGED, this.selectedFeatures)
    
    console.log('🎉 要素选择完成')
  }

  /**
   * 清除选择
   */
  private clearSelection() {
    console.log('🧹 清除选择')
    
    // 移除高亮
    this.selectedFeatures.forEach(feature => {
      if (feature.entity) {
        this.restoreOriginalStyle(feature.entity)
      }
    })
    
    this.selectedFeatures = []
    
    // 触发事件
    geoEventBus.emit(GEO_EVENTS.SELECTION_CHANGED, [])
  }

  /**
   * 创建高亮效果
   */
  private createHighlight(feature: GeoFeature) {
    if (!feature.entity) return

    console.log('✨ 创建高亮效果')
    
    const entity = feature.entity
    
    // 根据实体类型添加高亮
    if (entity.point) {
      entity.point.color = new Cesium.ConstantProperty(Cesium.Color.YELLOW)
      entity.point.pixelSize = new Cesium.ConstantProperty(12)
    }    if (entity.polyline) {
      entity.polyline.material = new Cesium.ColorMaterialProperty(Cesium.Color.YELLOW)
      entity.polyline.width = new Cesium.ConstantProperty(3)
    }
    
    if (entity.polygon) {
      entity.polygon.material = new Cesium.ColorMaterialProperty(Cesium.Color.YELLOW.withAlpha(0.5))
      entity.polygon.outline = new Cesium.ConstantProperty(true)
      entity.polygon.outlineColor = new Cesium.ConstantProperty(Cesium.Color.YELLOW)
    }
  }

  /**
   * 恢复原始样式
   */
  private restoreOriginalStyle(entity: Cesium.Entity) {
    // 这里应该恢复原始样式，暂时使用默认样式
    if (entity.point) {
      entity.point.color = new Cesium.ConstantProperty(Cesium.Color.WHITE)
      entity.point.pixelSize = new Cesium.ConstantProperty(8)
    }
      if (entity.polyline) {
      entity.polyline.material = new Cesium.ColorMaterialProperty(Cesium.Color.WHITE)
      entity.polyline.width = new Cesium.ConstantProperty(1)
    }
    
    if (entity.polygon) {
      entity.polygon.material = new Cesium.ColorMaterialProperty(Cesium.Color.WHITE.withAlpha(0.3))
      entity.polygon.outline = new Cesium.ConstantProperty(true)
      entity.polygon.outlineColor = new Cesium.ConstantProperty(Cesium.Color.WHITE)
    }
  }

  /**
   * 获取当前选择的要素
   */
  getSelectedFeatures(): GeoFeature[] {
    return this.selectedFeatures
  }

  /**
   * 销毁选择工具
   */
  destroy() {
    this.deactivate()
    console.log('🗑️ 直接选择工具已销毁')
  }
}
