import * as Cesium from "cesium"
import type { GeoFeature, FeatureType, GeoPoint } from '../core/types'
import { geoEventBus, GEO_EVENTS } from '../core/eventBus'

/**
 * 简单选择工具 - 专注于解决基本的选择问题
 */
export class SimpleSelectionTool {
  private viewer: Cesium.Viewer
  private isActive = false
  private selectedFeatures: GeoFeature[] = []
  private originalCameraControllerSettings: any = {}
  
  // 框选相关
  private isMouseDown = false
  private mouseDownPosition: { x: number, y: number } | null = null
  private selectionBox: HTMLElement | null = null
  
  // 事件处理器引用，用于清理
  private eventHandlers = {
    mousedown: null as ((event: MouseEvent) => void) | null,
    mousemove: null as ((event: MouseEvent) => void) | null,
    mouseup: null as ((event: MouseEvent) => void) | null,
    contextmenu: null as ((event: MouseEvent) => void) | null
  }
  
  constructor(viewer: Cesium.Viewer) {
    this.viewer = viewer
    this.createSelectionBox()
  }

  /**
   * 激活选择工具
   */
  activate() {
    console.log('🎯 激活简单选择工具')
    
    this.isActive = true
    
    // 保存原始相机控制器设置
    const controller = this.viewer.scene.screenSpaceCameraController
    this.originalCameraControllerSettings = {
      enableRotate: controller.enableRotate,
      enableTranslate: controller.enableTranslate,
      enableZoom: controller.enableZoom,
      enableTilt: controller.enableTilt,
      enableLook: controller.enableLook,
      enableInputs: controller.enableInputs
    }
    
    // 精细化相机控制：保持缩放，禁用平移和旋转
    controller.enableRotate = false
    controller.enableTranslate = false
    controller.enableZoom = true  // 保持缩放功能
    controller.enableTilt = false
    controller.enableLook = false
    // 不禁用 enableInputs，让缩放事件能正常工作
    
    this.setupEventHandlers()
    
    console.log('✅ 选择工具已激活，相机控制已禁用')
  }

  /**
   * 停用选择工具
   */
  deactivate() {
    console.log('🎯 停用简单选择工具')
    
    this.isActive = false
    
    // 恢复相机控制器设置
    const controller = this.viewer.scene.screenSpaceCameraController
    Object.assign(controller, this.originalCameraControllerSettings)
    
    this.clearEventHandlers()
    
    console.log('✅ 选择工具已停用，相机控制已恢复')
  }  /**
   * 设置事件处理器
   */
  private setupEventHandlers() {
    const canvas = this.viewer.scene.canvas
    if (!canvas) return

    console.log('🔧 设置完整的事件处理器（支持点击和框选）')
    
    // 阻止所有默认的 Cesium 事件
    canvas.style.cursor = 'crosshair'
    
    // 创建事件处理器
    this.eventHandlers.mousedown = (event: MouseEvent) => {
      if (!this.isActive || event.button !== 0) return
      
      console.log('🎯 鼠标按下事件')
      event.preventDefault()
      event.stopPropagation()
      event.stopImmediatePropagation()
      
      this.isMouseDown = true
      const rect = canvas.getBoundingClientRect()
      this.mouseDownPosition = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      }
      
      console.log('📍 按下位置:', this.mouseDownPosition)
    }
    
    this.eventHandlers.mousemove = (event: MouseEvent) => {
      if (!this.isActive || !this.isMouseDown || !this.mouseDownPosition) return
      
      event.preventDefault()
      event.stopPropagation()
      event.stopImmediatePropagation()
      
      const rect = canvas.getBoundingClientRect()
      const currentPos = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      }
      
      // 计算移动距离
      const distance = Math.sqrt(
        Math.pow(currentPos.x - this.mouseDownPosition.x, 2) +
        Math.pow(currentPos.y - this.mouseDownPosition.y, 2)
      )
      
      // 如果移动距离大于5像素，显示选择框
      if (distance > 5) {
        this.showSelectionBox(this.mouseDownPosition, currentPos)
      }
    }
    
    this.eventHandlers.mouseup = (event: MouseEvent) => {
      if (!this.isActive || event.button !== 0) return
      
      console.log('🎯 鼠标释放事件')
      event.preventDefault()
      event.stopPropagation()
      event.stopImmediatePropagation()
      
      if (this.isMouseDown && this.mouseDownPosition) {
        const rect = canvas.getBoundingClientRect()
        const currentPos = {
          x: event.clientX - rect.left,
          y: event.clientY - rect.top
        }
        
        const distance = Math.sqrt(
          Math.pow(currentPos.x - this.mouseDownPosition.x, 2) +
          Math.pow(currentPos.y - this.mouseDownPosition.y, 2)
        )
        
        if (distance <= 5) {
          // 单击选择
          console.log('执行单击选择')
          this.handleClickSelection(this.mouseDownPosition)
        } else {
          // 框选
          console.log('执行框选')
          this.handleBoxSelection(this.mouseDownPosition, currentPos)
        }
      }
      
      // 重置状态
      this.isMouseDown = false
      this.mouseDownPosition = null
      this.hideSelectionBox()
    }
    
    this.eventHandlers.contextmenu = (event: MouseEvent) => {
      if (this.isActive) {
        event.preventDefault()
        event.stopPropagation()
      }
    }
    
    // 注册事件监听器
    canvas.addEventListener('mousedown', this.eventHandlers.mousedown, true)
    canvas.addEventListener('mousemove', this.eventHandlers.mousemove, true)
    canvas.addEventListener('mouseup', this.eventHandlers.mouseup, true)
    canvas.addEventListener('contextmenu', this.eventHandlers.contextmenu, true)
  }  /**
   * 清理事件处理器
   */
  private clearEventHandlers() {
    const canvas = this.viewer.scene.canvas
    if (!canvas) return

    console.log('🧹 清理事件处理器')
    
    canvas.style.cursor = 'default'
    
    // 移除原生DOM事件监听器
    if (this.eventHandlers.mousedown) {
      canvas.removeEventListener('mousedown', this.eventHandlers.mousedown, true)
      this.eventHandlers.mousedown = null
    }
    
    if (this.eventHandlers.mousemove) {
      canvas.removeEventListener('mousemove', this.eventHandlers.mousemove, true)
      this.eventHandlers.mousemove = null
    }
    
    if (this.eventHandlers.mouseup) {
      canvas.removeEventListener('mouseup', this.eventHandlers.mouseup, true)
      this.eventHandlers.mouseup = null
    }
    
    if (this.eventHandlers.contextmenu) {
      canvas.removeEventListener('contextmenu', this.eventHandlers.contextmenu, true)
      this.eventHandlers.contextmenu = null
    }
  }

  /**
   * 处理点击选择
   */
  private handleClickSelection(position: { x: number, y: number }) {
    console.log('🎯 处理点击选择，位置:', position)
    
    // 创建 Cesium 坐标
    const cesiumPosition = new Cesium.Cartesian2(position.x, position.y)
    
    // 执行拾取
    const pickedObject = this.viewer.scene.pick(cesiumPosition)
    
    console.log('🎯 拾取结果:', pickedObject)
    
    if (Cesium.defined(pickedObject) && pickedObject.id) {
      const entity = pickedObject.id as Cesium.Entity
      console.log('✅ 选中实体:', entity.id, entity.name)
      
      // 转换为 GeoFeature
      const feature = this.convertEntityToGeoFeature(entity)
      if (feature) {
        this.selectFeature(feature)
      }
    } else {
      console.log('❌ 未选中任何实体')
      this.clearSelection()
    }
  }

  /**
   * 选择要素
   */
  private selectFeature(feature: GeoFeature) {
    console.log('✨ 选择要素:', feature)
    
    // 清除之前的选择
    this.clearSelection()
    
    // 添加到选择列表
    this.selectedFeatures.push(feature)
    
    // 创建高亮效果
    this.createHighlight(feature)
    
    // 触发事件
    geoEventBus.emit(GEO_EVENTS.FEATURE_SELECTED, feature)
    geoEventBus.emit(GEO_EVENTS.SELECTION_CHANGED, this.selectedFeatures)
    
    console.log('🎉 要素选择完成')
  }

  /**
   * 清除选择
   */
  private clearSelection() {
    console.log('🧹 清除选择')
    
    // 移除高亮
    this.selectedFeatures.forEach(feature => {
      if (feature.entity) {
        // 恢复原始样式
        this.restoreOriginalStyle(feature.entity)
      }
    })
    
    this.selectedFeatures = []
    
    // 触发事件
    geoEventBus.emit(GEO_EVENTS.SELECTION_CLEARED)
    geoEventBus.emit(GEO_EVENTS.SELECTION_CHANGED, [])
  }
  /**
   * 创建高亮效果
   */
  private createHighlight(feature: GeoFeature) {
    if (!feature.entity) return

    console.log('✨ 创建高亮效果')
    
    const entity = feature.entity
    
    // 根据实体类型添加高亮
    if (entity.point) {
      entity.point.color = new Cesium.ConstantProperty(Cesium.Color.YELLOW)
      entity.point.outlineColor = new Cesium.ConstantProperty(Cesium.Color.RED)
      entity.point.outlineWidth = new Cesium.ConstantProperty(3)
    }
    
    if (entity.polyline) {
      entity.polyline.material = new Cesium.ColorMaterialProperty(Cesium.Color.YELLOW)
      entity.polyline.width = new Cesium.ConstantProperty(5)
    }
    
    if (entity.polygon) {
      entity.polygon.material = new Cesium.ColorMaterialProperty(Cesium.Color.YELLOW.withAlpha(0.7))
      entity.polygon.outline = new Cesium.ConstantProperty(true)
      entity.polygon.outlineColor = new Cesium.ConstantProperty(Cesium.Color.RED)
    }
  }

  /**
   * 恢复原始样式
   */
  private restoreOriginalStyle(_entity: Cesium.Entity) {
    // 这里可以根据需要恢复原始样式
    // 暂时简单处理
  }

  /**
   * 将 Cesium 实体转换为 GeoFeature
   */
  private convertEntityToGeoFeature(entity: Cesium.Entity): GeoFeature | null {
    try {
      console.log('🔄 转换实体为要素:', entity.id)
      
      const points = this.extractPoints(entity)
      if (!points || points.length === 0) {
        console.warn('⚠️ 无法提取实体坐标点')
        return null
      }

      const feature: GeoFeature = {
        id: entity.id,
        type: this.getEntityType(entity),
        points: points,
        properties: {
          name: entity.name || `要素_${entity.id}`,
          description: entity.description?.getValue(Cesium.JulianDate.now()) || ''
        },
        timestamp: new Date(),
        entity: entity
      }

      console.log('✅ 实体转换完成:', feature)
      return feature
    } catch (error) {
      console.error('❌ 实体转换失败:', error)
      return null
    }
  }

  /**
   * 提取实体坐标点
   */
  private extractPoints(entity: Cesium.Entity): GeoPoint[] {
    const points: GeoPoint[] = []

    try {
      if (entity.position) {
        const position = entity.position.getValue(Cesium.JulianDate.now())
        if (position) {
          const cartographic = Cesium.Cartographic.fromCartesian(position)
          points.push({
            longitude: Cesium.Math.toDegrees(cartographic.longitude),
            latitude: Cesium.Math.toDegrees(cartographic.latitude),
            height: cartographic.height
          })
        }
      }
      
      if (entity.polyline?.positions) {
        const positions = entity.polyline.positions.getValue(Cesium.JulianDate.now())
        if (positions) {
          positions.forEach((pos: Cesium.Cartesian3) => {
            const cartographic = Cesium.Cartographic.fromCartesian(pos)
            points.push({
              longitude: Cesium.Math.toDegrees(cartographic.longitude),
              latitude: Cesium.Math.toDegrees(cartographic.latitude),
              height: cartographic.height
            })
          })
        }
      }
      
      if (entity.polygon?.hierarchy) {
        const hierarchy = entity.polygon.hierarchy.getValue(Cesium.JulianDate.now())
        if (hierarchy) {
          const positions = Array.isArray(hierarchy) ? hierarchy : hierarchy.positions
          if (positions) {
            positions.forEach((pos: Cesium.Cartesian3) => {
              const cartographic = Cesium.Cartographic.fromCartesian(pos)
              points.push({
                longitude: Cesium.Math.toDegrees(cartographic.longitude),
                latitude: Cesium.Math.toDegrees(cartographic.latitude),
                height: cartographic.height
              })
            })
          }
        }
      }
    } catch (error) {
      console.error('提取坐标点时出错:', error)
    }
    
    return points
  }

  /**
   * 获取实体类型
   */
  private getEntityType(entity: Cesium.Entity): FeatureType {
    if (entity.point) return 'point'
    if (entity.polyline) return 'line'  
    if (entity.polygon) return 'polygon'
    if (entity.ellipse) return 'circle'
    if (entity.rectangle) return 'rectangle'
    return 'point'
  }

  /**
   * 获取选中的要素
   */
  getSelectedFeatures(): GeoFeature[] {
    return [...this.selectedFeatures]
  }

  /**
   * 检查是否处于激活状态
   */
  isActivated(): boolean {
    return this.isActive
  }

  /**
   * 创建选择框元素
   */
  private createSelectionBox() {
    if (this.selectionBox) return

    this.selectionBox = document.createElement('div')
    this.selectionBox.style.position = 'absolute'
    this.selectionBox.style.border = '2px dashed #00ff00'
    this.selectionBox.style.backgroundColor = 'rgba(0, 255, 0, 0.1)'
    this.selectionBox.style.display = 'none'
    this.selectionBox.style.pointerEvents = 'none'
    this.selectionBox.style.zIndex = '9999'
    
    // 添加到 viewer 容器
    const container = this.viewer.container
    container.appendChild(this.selectionBox)
    
    console.log('📦 选择框已创建')
  }

  /**
   * 显示选择框
   */
  private showSelectionBox(start: { x: number, y: number }, end: { x: number, y: number }) {
    if (!this.selectionBox) return

    const minX = Math.min(start.x, end.x)
    const maxX = Math.max(start.x, end.x)
    const minY = Math.min(start.y, end.y)
    const maxY = Math.max(start.y, end.y)

    this.selectionBox.style.left = `${minX}px`
    this.selectionBox.style.top = `${minY}px`
    this.selectionBox.style.width = `${maxX - minX}px`
    this.selectionBox.style.height = `${maxY - minY}px`
    this.selectionBox.style.display = 'block'
  }

  /**
   * 隐藏选择框
   */
  private hideSelectionBox() {
    if (this.selectionBox) {
      this.selectionBox.style.display = 'none'
    }
  }

  /**
   * 处理框选
   */
  private handleBoxSelection(start: { x: number, y: number }, end: { x: number, y: number }) {
    console.log('📦 执行框选:', start, end)
    
    // 计算框选区域
    const minX = Math.min(start.x, end.x)
    const maxX = Math.max(start.x, end.x)
    const minY = Math.min(start.y, end.y)
    const maxY = Math.max(start.y, end.y)
    
    console.log('📦 框选区域:', { minX, minY, maxX, maxY })
    
    // 遍历所有实体，检查是否在框选区域内
    const selectedEntities: Cesium.Entity[] = []
    
    this.viewer.entities.values.forEach(entity => {
      if (this.isEntityInBox(entity, minX, minY, maxX, maxY)) {
        selectedEntities.push(entity)
      }
    })
    
    console.log('📦 框选到的实体数量:', selectedEntities.length)
    
    // 清除之前的选择
    this.clearSelection()
    
    // 选择框选到的实体
    selectedEntities.forEach(entity => {
      const feature = this.convertEntityToGeoFeature(entity)
      if (feature) {
        this.selectedFeatures.push(feature)
        this.createHighlight(feature)
      }
    })
    
    if (this.selectedFeatures.length > 0) {
      // 触发选择事件
      geoEventBus.emit(GEO_EVENTS.SELECTION_CHANGED, this.selectedFeatures)
      console.log('🎉 框选完成，选择了', this.selectedFeatures.length, '个要素')
    }
  }

  /**
   * 检查实体是否在框选区域内
   */
  private isEntityInBox(entity: Cesium.Entity, minX: number, minY: number, maxX: number, maxY: number): boolean {
    try {
      // 获取实体的屏幕坐标
      let position: Cesium.Cartesian3 | undefined
      
      if (entity.position) {
        position = entity.position.getValue(Cesium.JulianDate.now())
      } else if (entity.polyline?.positions) {
        const positions = entity.polyline.positions.getValue(Cesium.JulianDate.now())
        if (positions && positions.length > 0) {
          position = positions[0]
        }
      } else if (entity.polygon?.hierarchy) {
        const hierarchy = entity.polygon.hierarchy.getValue(Cesium.JulianDate.now())
        if (hierarchy) {
          const positions = Array.isArray(hierarchy) ? hierarchy : hierarchy.positions
          if (positions && positions.length > 0) {
            position = positions[0]
          }
        }
      }
      
      if (!position) return false
      
      // 转换为屏幕坐标
      const screenPosition = Cesium.SceneTransforms.worldToWindowCoordinates(this.viewer.scene, position)
      if (!screenPosition) return false
      
      // 检查是否在框选区域内
      return screenPosition.x >= minX && screenPosition.x <= maxX &&
             screenPosition.y >= minY && screenPosition.y <= maxY
    } catch (error) {
      console.warn('检查实体是否在框选区域内时出错:', error)
      return false
    }
  }

  /**
   * 销毁选择工具
   */
  destroy() {
    this.deactivate()
    
    // 清理选择框
    if (this.selectionBox && this.selectionBox.parentNode) {
      this.selectionBox.parentNode.removeChild(this.selectionBox)
      this.selectionBox = null
    }
    
    console.log('🗑️ 选择工具已销毁')
  }
}
