import { Role } from './role.entity';
import { UserSession } from './user-session.entity';
import { AuditLog } from './audit-log.entity';
export declare class User {
    user_id: string;
    username: string;
    email: string;
    password_hash: string;
    full_name: string;
    department: string;
    phone: string;
    is_active: boolean;
    is_first_login: boolean;
    last_login: Date;
    login_attempts: number;
    locked_until: Date | null;
    password_changed_at: Date | null;
    created_at: Date;
    updated_at: Date;
    created_by: string;
    roles: Role[];
    sessions: UserSession[];
    audit_logs: AuditLog[];
    password?: string;
    get id(): string;
    get fullName(): string;
    set fullName(value: string);
    get isActive(): boolean;
    set isActive(value: boolean);
    get isFirstLogin(): boolean;
    set isFirstLogin(value: boolean);
    get lastLoginAt(): Date;
    set lastLoginAt(value: Date);
    get failedLoginAttempts(): number;
    set failedLoginAttempts(value: number);
    get lockedAt(): Date | null;
    set lockedAt(value: Date | null);
    get lockExpiresAt(): Date | null;
    set lockExpiresAt(value: Date | null);
    get passwordHash(): string;
    set passwordHash(value: string);
    get passwordChangedAt(): Date | null;
    set passwordChangedAt(value: Date | null);
    get createdAt(): Date;
    get updatedAt(): Date;
    get isLocked(): boolean;
    hashPassword(): Promise<void>;
    validatePassword(password: string): Promise<boolean>;
    incrementLoginAttempts(): Promise<void>;
    resetLoginAttempts(): void;
    updateLastLogin(): void;
    getPermissions(): string[];
    hasPermission(permission: string): boolean;
    hasRole(roleName: string): boolean;
    toSafeObject(): Omit<this, "password_hash" | "login_attempts" | "locked_until" | "hashPassword" | "password" | "id" | "fullName" | "isActive" | "isFirstLogin" | "lastLoginAt" | "failedLoginAttempts" | "lockedAt" | "lockExpiresAt" | "passwordHash" | "passwordChangedAt" | "createdAt" | "updatedAt" | "isLocked" | "validatePassword" | "incrementLoginAttempts" | "resetLoginAttempts" | "updateLastLogin" | "getPermissions" | "hasPermission" | "hasRole" | "toSafeObject"> & {
        id: string;
        fullName: string;
        isActive: boolean;
        isFirstLogin: boolean;
        lastLoginAt: Date;
        failedLoginAttempts: number;
        isLocked: boolean;
        passwordChangedAt: Date | null;
        createdAt: Date;
        updatedAt: Date;
        roles: {
            id: string;
            name: string;
            displayName: string;
            description: string;
        }[];
        permissions: string[];
    };
}
