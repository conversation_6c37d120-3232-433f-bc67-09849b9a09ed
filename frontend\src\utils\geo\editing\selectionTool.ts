import * as Cesium from "cesium"
import type { GeoFeature, SelectionState, GeoEventCallbacks, FeatureType, GeoPoint, FeatureProperties } from '../core/types'
import { geoEventBus, GEO_EVENTS } from '../core/eventBus'

/**
 * 要素选择工具
 */
export class SelectionTool {
  private viewer: Cesium.Viewer
  private clickHandler: Cesium.ScreenSpaceEventHandler | null = null
  private selectionState: SelectionState = {
    selectedFeatures: [],
    isSelecting: false,
    selectionMode: 'single'
  }
  private highlightEntities: Map<string, Cesium.Entity> = new Map()
  private callbacks: GeoEventCallbacks = {}
  
  // 框选相关状态
  private isBoxSelecting = false
  private boxSelectStartPosition: Cesium.Cartesian2 | null = null
  private boxSelectRectangle: HTMLElement | null = null
  
  // 原生事件监听器
  private mouseDownHandler: ((event: MouseEvent) => void) | null = null
  private mouseMoveHandler: ((event: MouseEvent) => void) | null = null
  private mouseUpHandler: ((event: MouseEvent) => void) | null = null

  // 键盘状态跟踪
  private keyboardState = {
    ctrlPressed: false,
    shiftPressed: false
  }
  private keyDownHandler: ((event: KeyboardEvent) => void) | null = null
  private keyUpHandler: ((event: KeyboardEvent) => void) | null = null

  constructor(viewer: Cesium.Viewer) {
    this.viewer = viewer
    this.setupEventListeners()
    this.setupBoxSelectUI()
    this.setupKeyboardListeners()
  }

  /**
   * 设置事件回调
   */
  setEventCallbacks(callbacks: GeoEventCallbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks }
  }  /**
   * 激活选择工具
   */
  activate(mode: 'single' | 'multiple' = 'single') {
    this.deactivate()
    
    this.selectionState.isSelecting = true
    this.selectionState.selectionMode = mode
    
    // 精细化相机控制：保持缩放，禁用平移和旋转
    this.viewer.scene.screenSpaceCameraController.enableRotate = false
    this.viewer.scene.screenSpaceCameraController.enableTranslate = false
    this.viewer.scene.screenSpaceCameraController.enableZoom = true  // 保持缩放功能
    this.viewer.scene.screenSpaceCameraController.enableTilt = false
    this.viewer.scene.screenSpaceCameraController.enableLook = false
    // 保持 enableInputs 为 true，让缩放事件能正常工作
    
    // 禁用默认的双击行为
    this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK)
    
    this.setupClickHandler()
    
    console.log(`🎯 选择工具已激活 (${mode} 模式) - 所有相机控制已禁用`)
    console.log('📍 相机控制器状态:', {
      enableRotate: this.viewer.scene.screenSpaceCameraController.enableRotate,
      enableTranslate: this.viewer.scene.screenSpaceCameraController.enableTranslate,
      enableZoom: this.viewer.scene.screenSpaceCameraController.enableZoom,
      enableInputs: this.viewer.scene.screenSpaceCameraController.enableInputs
    })
    
    geoEventBus.emit(GEO_EVENTS.TOOL_ACTIVATED, 'selection')
  }
  /**
   * 停用选择工具
   */
  deactivate() {
    // 清理 Cesium 事件处理器
    if (this.clickHandler) {
      this.clickHandler.destroy()
      this.clickHandler = null
    }
    
    // 清理原生事件监听器
    if (this.viewer.scene.canvas) {
      const canvas = this.viewer.scene.canvas

      if (this.mouseDownHandler) {
        canvas.removeEventListener('mousedown', this.mouseDownHandler, true)
        this.mouseDownHandler = null
      }

      if (this.mouseMoveHandler) {
        canvas.removeEventListener('mousemove', this.mouseMoveHandler, true)
        this.mouseMoveHandler = null
      }

      if (this.mouseUpHandler) {
        canvas.removeEventListener('mouseup', this.mouseUpHandler, true)
        this.mouseUpHandler = null
      }
    }

    // 清理键盘事件监听器
    if (this.keyDownHandler) {
      document.removeEventListener('keydown', this.keyDownHandler, true)
      this.keyDownHandler = null
    }

    if (this.keyUpHandler) {
      document.removeEventListener('keyup', this.keyUpHandler, true)
      this.keyUpHandler = null
    }
    
    // 重新启用相机控制器
    this.viewer.scene.screenSpaceCameraController.enableRotate = true
    this.viewer.scene.screenSpaceCameraController.enableTranslate = true
    this.viewer.scene.screenSpaceCameraController.enableZoom = true
    this.viewer.scene.screenSpaceCameraController.enableTilt = true
    this.viewer.scene.screenSpaceCameraController.enableLook = true
    this.viewer.scene.screenSpaceCameraController.enableInputs = true
    
    this.selectionState.isSelecting = false
    
    // 隐藏框选矩形
    if (this.boxSelectRectangle) {
      this.boxSelectRectangle.style.display = 'none'
    }
    
    console.log('🎯 选择工具已停用 - 相机控制已恢复')
    console.log('📍 相机控制器状态:', {
      enableRotate: this.viewer.scene.screenSpaceCameraController.enableRotate,
      enableTranslate: this.viewer.scene.screenSpaceCameraController.enableTranslate,
      enableZoom: this.viewer.scene.screenSpaceCameraController.enableZoom,
      enableInputs: this.viewer.scene.screenSpaceCameraController.enableInputs
    })
    
    geoEventBus.emit(GEO_EVENTS.TOOL_DEACTIVATED, 'selection')
  }

  /**
   * 选择要素
   * @param feature 要选择的要素
   * @param mode 选择模式: 'replace' | 'toggle' | 'add'
   */
  selectFeature(feature: GeoFeature, mode: 'replace' | 'toggle' | 'add' = 'replace') {
    const isAlreadySelected = this.selectionState.selectedFeatures.some(f => f.id === feature.id)

    console.log(`🎯 选择要素 ${feature.id}，模式: ${mode}，已选择: ${isAlreadySelected}`)

    switch (mode) {
      case 'replace':
        // 替换选择：清除所有选择，然后选择当前要素
        this.clearSelection()
        this.addFeatureToSelection(feature)
        break

      case 'toggle':
        // 切换选择：如果已选择则取消，否则添加
        if (isAlreadySelected) {
          this.deselectFeature(feature.id)
        } else {
          this.addFeatureToSelection(feature)
        }
        break

      case 'add':
        // 追加选择：只添加，不取消
        if (!isAlreadySelected) {
          this.addFeatureToSelection(feature)
        }
        break
    }
  }

  /**
   * 添加要素到选择集合
   */
  private addFeatureToSelection(feature: GeoFeature) {
    // 添加到选择集合
    this.selectionState.selectedFeatures.push(feature)

    // 创建高亮效果
    this.createHighlight(feature)

    // 触发事件
    this.callbacks.onFeatureSelected?.(feature)
    geoEventBus.emit(GEO_EVENTS.FEATURE_SELECTED, feature)
    geoEventBus.emit(GEO_EVENTS.SELECTION_CHANGED, this.selectionState.selectedFeatures)

    console.log(`✅ 要素已添加到选择: ${feature.id}`)
  }

  /**
   * 取消选择要素
   */
  deselectFeature(featureId: string) {
    const index = this.selectionState.selectedFeatures.findIndex(f => f.id === featureId)
    if (index === -1) return

    // 从选择集合中移除
    this.selectionState.selectedFeatures.splice(index, 1)
    
    // 移除高亮效果
    this.removeHighlight(featureId)
    
    // 触发事件
    this.callbacks.onFeatureDeselected?.(featureId)
    geoEventBus.emit(GEO_EVENTS.FEATURE_DESELECTED, featureId)
    geoEventBus.emit(GEO_EVENTS.SELECTION_CHANGED, this.selectionState.selectedFeatures)
    
    console.log(`❌ 要素取消选择: ${featureId}`)
  }

  /**
   * 清除所有选择
   */
  clearSelection() {
    const selectedIds = this.selectionState.selectedFeatures.map(f => f.id)
    
    this.selectionState.selectedFeatures = []
    this.clearAllHighlights()
    
    // 触发事件
    selectedIds.forEach(id => {
      this.callbacks.onFeatureDeselected?.(id)
      geoEventBus.emit(GEO_EVENTS.FEATURE_DESELECTED, id)
    })
    
    geoEventBus.emit(GEO_EVENTS.SELECTION_CLEARED)
    geoEventBus.emit(GEO_EVENTS.SELECTION_CHANGED, [])
    
    console.log('🧹 已清除所有选择')
  }

  /**
   * 获取选择状态
   */
  getSelectionState(): SelectionState {
    return { ...this.selectionState }
  }

  /**
   * 获取选中的要素
   */
  getSelectedFeatures(): GeoFeature[] {
    return [...this.selectionState.selectedFeatures]
  }

  /**
   * 通过ID选择要素
   */
  selectById(featureId: string, features: GeoFeature[], mode: 'replace' | 'toggle' | 'add' = 'replace') {
    const feature = features.find(f => f.id === featureId)
    if (feature) {
      this.selectFeature(feature, mode)
    }
  }

  /**
   * 通过位置选择要素
   */
  selectByPosition(windowPosition: Cesium.Cartesian2, features: GeoFeature[]) {
    const pickedObject = this.viewer.scene.pick(windowPosition)

    if (pickedObject && pickedObject.id) {
      const entityId = pickedObject.id.id || pickedObject.id
      const feature = features.find(f => f.id === entityId)

      if (feature) {
        const isCtrlPressed = this.isCtrlPressed()
        const isShiftPressed = this.isShiftPressed()

        let mode: 'replace' | 'toggle' | 'add' = 'replace'
        if (this.selectionState.selectionMode === 'multiple') {
          if (isCtrlPressed) {
            mode = 'toggle'
          } else if (isShiftPressed) {
            mode = 'add'
          }
        }

        this.selectFeature(feature, mode)
      }
    } else {
      // 点击空白区域，清除选择
      if (!this.isCtrlPressed()) {
        this.clearSelection()
      }
    }
  }

  /**
   * 框选要素
   */
  selectByBounds(startPosition: Cesium.Cartesian2, endPosition: Cesium.Cartesian2, features: GeoFeature[]) {
    // 创建选择框
    const rectangle = this.createSelectionRectangle(startPosition, endPosition)
    
    // 找到框内的要素
    const selectedFeatures = features.filter(feature => {
      return this.isFeatureInRectangle(feature, rectangle)
    })
    
    // 选择要素 - 框选默认使用追加模式
    const mode = this.isCtrlPressed() ? 'toggle' : (this.isShiftPressed() ? 'add' : 'add')
    selectedFeatures.forEach(feature => {
      this.selectFeature(feature, mode)
    })
    
    console.log(`📦 框选了 ${selectedFeatures.length} 个要素`)
  }  /**
   * 设置点击处理器 - 使用原生 DOM 事件
   */
  private setupClickHandler() {
    if (!this.viewer.scene.canvas) return

    const canvas = this.viewer.scene.canvas
    
    console.log('🎯 设置原生 DOM 事件监听器')
    
    // 鼠标按下事件
    this.mouseDownHandler = (event: MouseEvent) => {
      if (event.button === 0) { // 左键
        console.log('🎯 原生 mousedown 事件触发')
        event.stopPropagation()
        event.preventDefault()
        
        const rect = canvas.getBoundingClientRect()
        const position = new Cesium.Cartesian2(
          event.clientX - rect.left,
          event.clientY - rect.top
        )
        
        this.boxSelectStartPosition = position
        this.isBoxSelecting = false
      }
    }
    
    // 鼠标移动事件
    this.mouseMoveHandler = (event: MouseEvent) => {
      if (this.boxSelectStartPosition) {
        console.log('🎯 原生 mousemove 事件触发')
        event.stopPropagation()
        event.preventDefault()
        
        const rect = canvas.getBoundingClientRect()
        const currentPosition = new Cesium.Cartesian2(
          event.clientX - rect.left,
          event.clientY - rect.top
        )
        
        const distance = Cesium.Cartesian2.distance(this.boxSelectStartPosition, currentPosition)
        
        if (distance > 5 && !this.isBoxSelecting) {
          console.log('🎯 开始框选模式')
          this.isBoxSelecting = true
          this.startBoxSelect(this.boxSelectStartPosition)
        }
        
        if (this.isBoxSelecting) {
          this.updateBoxSelect(currentPosition)
        }
      }
    }
    
    // 鼠标释放事件
    this.mouseUpHandler = (event: MouseEvent) => {
      if (event.button === 0 && this.boxSelectStartPosition) { // 左键
        console.log('🎯 原生 mouseup 事件触发')
        event.stopPropagation()
        event.preventDefault()
        
        const rect = canvas.getBoundingClientRect()
        const position = new Cesium.Cartesian2(
          event.clientX - rect.left,
          event.clientY - rect.top
        )
        
        if (this.isBoxSelecting) {
          console.log('🎯 完成框选')
          this.finishBoxSelect(position)
        } else {
          console.log('🎯 执行点击选择')
          this.handleClick(position)
        }
        
        this.boxSelectStartPosition = null
        this.isBoxSelecting = false
      }
    }
    
    // 添加事件监听器
    canvas.addEventListener('mousedown', this.mouseDownHandler, true)
    canvas.addEventListener('mousemove', this.mouseMoveHandler, true)
    canvas.addEventListener('mouseup', this.mouseUpHandler, true)
    
    // 禁用右键菜单
    canvas.addEventListener('contextmenu', (event) => {
      event.preventDefault()
      this.clearSelection()
    }, true)
  }
  /**
   * 处理点击事件
   */
  private handleClick(position: Cesium.Cartesian2) {
    console.log('🎯 处理点击事件，位置:', position)
    
    // 使用 Cesium 的拾取功能检测点击的实体
    const pickedObject = this.viewer.scene.pick(position)
    
    console.log('🎯 拾取的对象:', pickedObject)
    
    if (Cesium.defined(pickedObject) && pickedObject.id) {
      const entity = pickedObject.id
      console.log('🎯 选中的实体:', entity.id, entity.name)
      
      // 将 Cesium 实体转换为 GeoFeature
      const feature = this.convertEntityToGeoFeature(entity)
      
      if (feature) {
        console.log('🎯 转换的要素:', feature)

        // 检查键盘状态
        const isCtrlPressed = this.isCtrlPressed()
        const isShiftPressed = this.isShiftPressed()

        // 确定选择模式
        let selectionMode: 'replace' | 'toggle' | 'add' = 'replace'

        if (this.selectionState.selectionMode === 'multiple') {
          if (isCtrlPressed) {
            selectionMode = 'toggle' // Ctrl+点击：切换选择状态
          } else if (isShiftPressed) {
            selectionMode = 'add' // Shift+点击：追加选择
          }
          // 否则使用默认的 'replace' 模式
        }

        console.log('🎯 选择状态:', {
          selectionMode: this.selectionState.selectionMode,
          ctrlPressed: isCtrlPressed,
          shiftPressed: isShiftPressed,
          finalMode: selectionMode
        })

        this.selectFeature(feature, selectionMode)
      } else {
        console.warn('🎯 无法转换实体为要素:', entity)
      }
    } else {
      console.log('🎯 未拾取到实体，点击空白处')
      // 点击空白处时，如果没有按Ctrl键则清除选择
      if (!this.isCtrlPressed()) {
        console.log('🎯 清除选择（未按Ctrl键）')
        this.clearSelection()
      } else {
        console.log('🎯 保持选择（按住Ctrl键）')
      }
    }
  }/**
   * 将 Cesium 实体转换为 GeoFeature
   */
  private convertEntityToGeoFeature(entity: Cesium.Entity): GeoFeature | null {
    if (!entity) return null

    // 提取坐标点
    const points = this.extractPoints(entity)
    if (!points || points.length === 0) return null

    // 基础要素信息
    const feature: GeoFeature = {
      id: entity.id,
      type: this.getEntityGeometryType(entity),
      points: points,
      properties: {
        name: entity.name || entity.id,
        description: entity.description?.getValue(Cesium.JulianDate.now()) || '',
        ...this.extractStyleProperties(entity)
      },
      timestamp: new Date(),
      entity: entity,
      layerName: this.extractLayerName(entity) // 添加图层名称
    }

    return feature
  }

  /**
   * 提取实体的坐标点
   */
  private extractPoints(entity: Cesium.Entity): GeoPoint[] {
    const points: GeoPoint[] = []

    if (entity.position) {
      const position = entity.position.getValue(Cesium.JulianDate.now())
      if (position) {
        const cartographic = Cesium.Cartographic.fromCartesian(position)
        points.push({
          longitude: Cesium.Math.toDegrees(cartographic.longitude),
          latitude: Cesium.Math.toDegrees(cartographic.latitude),
          height: cartographic.height
        })
      }
    }
    
    if (entity.polyline?.positions) {
      const positions = entity.polyline.positions.getValue(Cesium.JulianDate.now())
      if (positions) {
        positions.forEach((pos: Cesium.Cartesian3) => {
          const cartographic = Cesium.Cartographic.fromCartesian(pos)
          points.push({
            longitude: Cesium.Math.toDegrees(cartographic.longitude),
            latitude: Cesium.Math.toDegrees(cartographic.latitude),
            height: cartographic.height
          })
        })
      }
    }
    
    if (entity.polygon?.hierarchy) {
      const hierarchy = entity.polygon.hierarchy.getValue(Cesium.JulianDate.now())
      if (hierarchy) {
        const positions = Array.isArray(hierarchy) ? hierarchy : hierarchy.positions
        positions.forEach((pos: Cesium.Cartesian3) => {
          const cartographic = Cesium.Cartographic.fromCartesian(pos)
          points.push({
            longitude: Cesium.Math.toDegrees(cartographic.longitude),
            latitude: Cesium.Math.toDegrees(cartographic.latitude),
            height: cartographic.height
          })
        })
      }
    }
    
    return points
  }

  /**
   * 提取样式属性
   */
  private extractStyleProperties(entity: Cesium.Entity): Partial<FeatureProperties> {
    const styleProps: Partial<FeatureProperties> = {}
    
    if (entity.point) {
      const pointSize = entity.point.pixelSize?.getValue(Cesium.JulianDate.now())
      const pointColor = entity.point.color?.getValue(Cesium.JulianDate.now())
      if (pointSize) styleProps.strokeWidth = pointSize
      if (pointColor) styleProps.color = pointColor.toCssColorString()
    }
    
    if (entity.polyline) {
      const lineWidth = entity.polyline.width?.getValue(Cesium.JulianDate.now())
      const lineMaterial = entity.polyline.material?.getValue(Cesium.JulianDate.now())
      if (lineWidth) styleProps.strokeWidth = lineWidth
      if (lineMaterial && lineMaterial.color) {
        styleProps.strokeColor = lineMaterial.color.toCssColorString()
      }
    }
    
    if (entity.polygon) {
      const fillMaterial = entity.polygon.material?.getValue(Cesium.JulianDate.now())
      const outlineColor = entity.polygon.outlineColor?.getValue(Cesium.JulianDate.now())
      if (fillMaterial && fillMaterial.color) {
        styleProps.fillColor = fillMaterial.color.toCssColorString()
        styleProps.fillOpacity = fillMaterial.color.alpha
      }
      if (outlineColor) {
        styleProps.strokeColor = outlineColor.toCssColorString()
      }
    }
    
    return styleProps
  }

  /**
   * 提取图层名称
   */
  private extractLayerName(entity: Cesium.Entity): string {
    // 尝试从实体的数据源获取图层名称
    if (entity.entityCollection && entity.entityCollection.owner) {
      const dataSource = entity.entityCollection.owner as Cesium.DataSource
      if (dataSource.name) {
        return dataSource.name
      }
    }

    // 尝试从实体名称中提取图层信息
    if (entity.name && entity.name.includes('_')) {
      const parts = entity.name.split('_')
      if (parts.length > 1) {
        return parts[0] // 假设图层名称在第一部分
      }
    }

    // 默认图层名称
    return '未知图层'
  }
  /**
   * 获取实体的几何类型
   */
  private getEntityGeometryType(entity: Cesium.Entity): FeatureType {
    if (entity.point) return 'point'
    if (entity.polyline) return 'line'
    if (entity.polygon) return 'polygon'
    if (entity.ellipse) return 'circle'
    if (entity.rectangle) return 'rectangle'
    return 'point' // 默认类型
  }
  /**
   * 创建高亮效果
   */
  private createHighlight(feature: GeoFeature) {
    if (!feature.entity) return

    // 根据要素类型创建不同的高亮效果
    let highlightEntity: Cesium.Entity | null = null

    switch (feature.type) {
      case 'point':
        highlightEntity = this.createPointHighlight(feature)
        break
      case 'line':
        highlightEntity = this.createLineHighlight(feature)
        break
      case 'polygon':
        highlightEntity = this.createPolygonHighlight(feature)
        break
      case 'circle':
        highlightEntity = this.createCircleHighlight(feature)
        break
      case 'rectangle':
        highlightEntity = this.createRectangleHighlight(feature)
        break
    }

    if (highlightEntity) {
      this.highlightEntities.set(feature.id, highlightEntity)
    }
  }

  /**
   * 创建点高亮
   */
  private createPointHighlight(feature: GeoFeature): Cesium.Entity {
    const position = feature.entity?.position
    
    return this.viewer.entities.add({
      position: position,
      point: {
        pixelSize: 15,
        color: Cesium.Color.YELLOW.withAlpha(0.7),
        outlineColor: Cesium.Color.RED,
        outlineWidth: 3,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    })
  }

  /**
   * 创建线高亮
   */
  private createLineHighlight(feature: GeoFeature): Cesium.Entity {
    const positions = feature.entity?.polyline?.positions
    
    return this.viewer.entities.add({
      polyline: {
        positions: positions,
        clampToGround: true,
        width: 8,
        material: Cesium.Color.YELLOW.withAlpha(0.7)
      }
    })
  }

  /**
   * 创建多边形高亮
   */
  private createPolygonHighlight(feature: GeoFeature): Cesium.Entity {
    const hierarchy = feature.entity?.polygon?.hierarchy
    
    return this.viewer.entities.add({
      polygon: {
        hierarchy: hierarchy,
        material: Cesium.Color.YELLOW.withAlpha(0.3),
        outline: true,
        outlineColor: Cesium.Color.RED,
        outlineWidth: 3
      }
    })
  }

  /**
   * 创建圆形高亮
   */
  private createCircleHighlight(feature: GeoFeature): Cesium.Entity {
    // 实现圆形高亮
    return this.viewer.entities.add({
      position: feature.entity?.position,      ellipse: {
        semiMajorAxis: 50, // 临时值，需要从原要素获取
        semiMinorAxis: 50,
        material: Cesium.Color.YELLOW.withAlpha(0.3),
        outline: true,
        outlineColor: Cesium.Color.RED
      }
    })
  }

  /**
   * 创建矩形高亮
   */
  private createRectangleHighlight(feature: GeoFeature): Cesium.Entity {
    // 实现矩形高亮
    const coordinates = feature.entity?.rectangle?.coordinates
    
    return this.viewer.entities.add({
      rectangle: {
        coordinates: coordinates,
        material: Cesium.Color.YELLOW.withAlpha(0.3),
        outline: true,
        outlineColor: Cesium.Color.RED
      }
    })
  }

  /**
   * 移除高亮效果
   */
  private removeHighlight(featureId: string) {
    const highlightEntity = this.highlightEntities.get(featureId)
    if (highlightEntity) {
      this.viewer.entities.remove(highlightEntity)
      this.highlightEntities.delete(featureId)
    }
  }

  /**
   * 清除所有高亮效果
   */
  private clearAllHighlights() {
    this.highlightEntities.forEach(entity => {
      this.viewer.entities.remove(entity)
    })
    this.highlightEntities.clear()
  }

  /**
   * 创建选择矩形
   */
  private createSelectionRectangle(start: Cesium.Cartesian2, end: Cesium.Cartesian2): Cesium.Rectangle {
    const startCartographic = this.viewer.camera.pickEllipsoid(start, this.viewer.scene.globe.ellipsoid)
    const endCartographic = this.viewer.camera.pickEllipsoid(end, this.viewer.scene.globe.ellipsoid)
    
    if (!startCartographic || !endCartographic) {
      return new Cesium.Rectangle()
    }
    
    const startLonLat = Cesium.Cartographic.fromCartesian(startCartographic)
    const endLonLat = Cesium.Cartographic.fromCartesian(endCartographic)
    
    return new Cesium.Rectangle(
      Math.min(startLonLat.longitude, endLonLat.longitude),
      Math.min(startLonLat.latitude, endLonLat.latitude),
      Math.max(startLonLat.longitude, endLonLat.longitude),
      Math.max(startLonLat.latitude, endLonLat.latitude)
    )
  }

  /**
   * 检查要素是否在矩形内
   */
  private isFeatureInRectangle(feature: GeoFeature, rectangle: Cesium.Rectangle): boolean {
    // 简单实现：检查要素的所有点是否在矩形内
    return feature.points.some(point => {
      const longitude = Cesium.Math.toRadians(point.longitude)
      const latitude = Cesium.Math.toRadians(point.latitude)
      
      return longitude >= rectangle.west && longitude <= rectangle.east &&
             latitude >= rectangle.south && latitude <= rectangle.north
    })
  }

  /**
   * 检查Ctrl键是否按下
   */
  private isCtrlPressed(): boolean {
    return this.keyboardState.ctrlPressed
  }

  /**
   * 检查Shift键是否按下
   */
  private isShiftPressed(): boolean {
    return this.keyboardState.shiftPressed
  }

  /**
   * 设置键盘事件监听器
   */
  private setupKeyboardListeners() {
    console.log('🎯 设置键盘事件监听器')

    this.keyDownHandler = (event: KeyboardEvent) => {
      if (event.key === 'Control' || event.ctrlKey) {
        this.keyboardState.ctrlPressed = true
        console.log('🎯 Ctrl键按下')
      }
      if (event.key === 'Shift' || event.shiftKey) {
        this.keyboardState.shiftPressed = true
        console.log('🎯 Shift键按下')
      }
    }

    this.keyUpHandler = (event: KeyboardEvent) => {
      if (event.key === 'Control' || !event.ctrlKey) {
        this.keyboardState.ctrlPressed = false
        console.log('🎯 Ctrl键释放')
      }
      if (event.key === 'Shift' || !event.shiftKey) {
        this.keyboardState.shiftPressed = false
        console.log('🎯 Shift键释放')
      }
    }

    // 添加全局键盘事件监听器
    document.addEventListener('keydown', this.keyDownHandler, true)
    document.addEventListener('keyup', this.keyUpHandler, true)

    // 监听窗口失焦事件，重置键盘状态
    window.addEventListener('blur', () => {
      this.keyboardState.ctrlPressed = false
      this.keyboardState.shiftPressed = false
      console.log('🎯 窗口失焦，重置键盘状态')
    })
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners() {
    // 监听要素列表请求
    geoEventBus.on('selection:requestFeatures', (position: Cesium.Cartesian2) => {
      // 这里需要获取当前的要素列表
      // 可以通过回调或事件获取
      console.log('请求要素列表用于选择:', position)
    })
  }

  /**
   * 设置框选 UI
   */
  private setupBoxSelectUI() {
    // 创建框选矩形元素
    this.boxSelectRectangle = document.createElement('div')
    this.boxSelectRectangle.style.position = 'absolute'
    this.boxSelectRectangle.style.border = '2px dashed #007bff'
    this.boxSelectRectangle.style.backgroundColor = 'rgba(0, 123, 255, 0.1)'
    this.boxSelectRectangle.style.display = 'none'
    this.boxSelectRectangle.style.pointerEvents = 'none'
    this.boxSelectRectangle.style.zIndex = '1000'
    
    // 添加到 canvas 的父容器
    if (this.viewer.canvas.parentElement) {
      this.viewer.canvas.parentElement.appendChild(this.boxSelectRectangle)
    }
  }

  /**
   * 开始框选
   */
  private startBoxSelect(startPosition: Cesium.Cartesian2) {
    if (!this.boxSelectRectangle) return
    
    const canvasRect = this.viewer.canvas.getBoundingClientRect()
    
    this.boxSelectRectangle.style.left = (canvasRect.left + startPosition.x) + 'px'
    this.boxSelectRectangle.style.top = (canvasRect.top + startPosition.y) + 'px'
    this.boxSelectRectangle.style.width = '0px'
    this.boxSelectRectangle.style.height = '0px'
    this.boxSelectRectangle.style.display = 'block'
  }

  /**
   * 更新框选区域
   */
  private updateBoxSelect(currentPosition: Cesium.Cartesian2) {
    if (!this.boxSelectRectangle || !this.boxSelectStartPosition) return
    
    const canvasRect = this.viewer.canvas.getBoundingClientRect()
    const minX = Math.min(this.boxSelectStartPosition.x, currentPosition.x)
    const minY = Math.min(this.boxSelectStartPosition.y, currentPosition.y)
    const maxX = Math.max(this.boxSelectStartPosition.x, currentPosition.x)
    const maxY = Math.max(this.boxSelectStartPosition.y, currentPosition.y)
    
    this.boxSelectRectangle.style.left = (canvasRect.left + minX) + 'px'
    this.boxSelectRectangle.style.top = (canvasRect.top + minY) + 'px'
    this.boxSelectRectangle.style.width = (maxX - minX) + 'px'
    this.boxSelectRectangle.style.height = (maxY - minY) + 'px'
  }

  /**
   * 完成框选
   */
  private finishBoxSelect(endPosition: Cesium.Cartesian2) {
    if (!this.boxSelectRectangle || !this.boxSelectStartPosition) return
    
    // 隐藏框选矩形
    this.boxSelectRectangle.style.display = 'none'
    
    // 计算框选区域
    const minX = Math.min(this.boxSelectStartPosition.x, endPosition.x)
    const minY = Math.min(this.boxSelectStartPosition.y, endPosition.y)
    const maxX = Math.max(this.boxSelectStartPosition.x, endPosition.x)
    const maxY = Math.max(this.boxSelectStartPosition.y, endPosition.y)
      // 在框选区域内查找实体
    const selectedEntities: Cesium.Entity[] = []
    
    // 遍历所有实体，检查是否在框选区域内
    this.viewer.entities.values.forEach(entity => {
      if (this.isEntityInBox(entity, minX, minY, maxX, maxY)) {
        selectedEntities.push(entity)
      }
    })
    
    // 转换为 GeoFeature 并选择
    if (this.selectionState.selectionMode === 'single') {
      this.clearSelection()
    }
    
    selectedEntities.forEach(entity => {
      const feature = this.convertEntityToGeoFeature(entity)
      if (feature) {
        this.selectFeature(feature, true)
      }
    })
    
    console.log(`📦 框选了 ${selectedEntities.length} 个要素`)
  }

  /**
   * 检查实体是否在框选区域内
   */
  private isEntityInBox(entity: Cesium.Entity, minX: number, minY: number, maxX: number, maxY: number): boolean {
    try {
      let position: Cesium.Cartesian3 | undefined
      
      // 获取实体的位置
      if (entity.position) {
        position = entity.position.getValue(Cesium.JulianDate.now())
      } else if (entity.polyline?.positions) {
        const positions = entity.polyline.positions.getValue(Cesium.JulianDate.now())
        if (positions && positions.length > 0) {
          position = positions[0] // 使用第一个点
        }
      } else if (entity.polygon?.hierarchy) {
        const hierarchy = entity.polygon.hierarchy.getValue(Cesium.JulianDate.now())
        if (hierarchy) {
          const positions = Array.isArray(hierarchy) ? hierarchy : hierarchy.positions
          if (positions && positions.length > 0) {
            position = positions[0] // 使用第一个点
          }
        }
      }
      
      if (!position) return false
        // 将世界坐标转换为屏幕坐标
      const screenPosition = Cesium.SceneTransforms.worldToWindowCoordinates(this.viewer.scene, position)
      if (!screenPosition) return false
      
      // 检查是否在框选区域内
      return screenPosition.x >= minX && screenPosition.x <= maxX &&
             screenPosition.y >= minY && screenPosition.y <= maxY
    } catch (error) {
      console.warn('检查实体位置时出错:', error)
      return false
    }
  }

  /**
   * 销毁选择工具
   */
  destroy() {
    this.deactivate()
    
    // 清理框选 UI
    if (this.boxSelectRectangle && this.boxSelectRectangle.parentElement) {
      this.boxSelectRectangle.parentElement.removeChild(this.boxSelectRectangle)
    }
    
    // 清理高亮
    this.clearSelection()
  }
}
