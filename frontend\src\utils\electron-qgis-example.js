/**
 * Electron 主进程中的 QGIS 启动器示例
 * 
 * 这个文件展示了如何在 Electron 主进程中实现 QGIS 启动功能
 * 如果您的应用需要打包为桌面应用，可以参考这个实现
 */

const { ipcMain, shell } = require('electron')
const { spawn, exec } = require('child_process')
const path = require('path')
const fs = require('fs')
const os = require('os')

/**
 * 注册 IPC 处理器
 */
function registerQGISHandlers() {
  // 处理 QGIS 启动请求
  ipcMain.handle('open-qgis', async () => {
    try {
      console.log('收到 QGIS 启动请求')
      const result = await launchQGIS()
      return result
    } catch (error) {
      console.error('QGIS 启动失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  })

  // 检查 QGIS 安装状态
  ipcMain.handle('check-qgis-installation', async () => {
    try {
      const isInstalled = await checkQGISInstallation()
      return { installed: isInstalled }
    } catch (error) {
      return { installed: false, error: error.message }
    }
  })
}

/**
 * 启动 QGIS 应用程序
 */
async function launchQGIS() {
  const platform = os.platform()
  
  try {
    switch (platform) {
      case 'win32':
        return await launchQGISWindows()
      case 'darwin':
        return await launchQGISMacOS()
      case 'linux':
        return await launchQGISLinux()
      default:
        throw new Error(`不支持的操作系统: ${platform}`)
    }
  } catch (error) {
    console.error('QGIS 启动失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Windows 平台启动 QGIS
 */
async function launchQGISWindows() {
  const possiblePaths = [
    'C:\\Program Files\\QGIS 3.34\\bin\\qgis.exe',
    'C:\\Program Files\\QGIS 3.32\\bin\\qgis.exe',
    'C:\\Program Files\\QGIS 3.30\\bin\\qgis.exe',
    'C:\\Program Files\\QGIS 3.28\\bin\\qgis.exe',
    'C:\\OSGeo4W64\\bin\\qgis.exe',
    'C:\\OSGeo4W\\bin\\qgis.exe'
  ]

  // 尝试从注册表获取路径
  try {
    const registryPath = await getQGISPathFromRegistry()
    if (registryPath) {
      possiblePaths.unshift(registryPath)
    }
  } catch (error) {
    console.warn('无法从注册表获取 QGIS 路径:', error.message)
  }

  for (const qgisPath of possiblePaths) {
    if (fs.existsSync(qgisPath)) {
      try {
        console.log(`尝试启动 QGIS: ${qgisPath}`)
        
        // 使用 spawn 启动 QGIS
        const child = spawn(qgisPath, [], {
          detached: true,
          stdio: 'ignore'
        })
        
        child.unref()
        
        return {
          success: true,
          path: qgisPath
        }
      } catch (error) {
        console.warn(`启动失败 ${qgisPath}:`, error.message)
        continue
      }
    }
  }

  throw new Error('未找到 QGIS 安装路径')
}

/**
 * macOS 平台启动 QGIS
 */
async function launchQGISMacOS() {
  const possiblePaths = [
    '/Applications/QGIS.app',
    '/Applications/QGIS-LTR.app',
    '/Applications/QGIS 3.34.app',
    '/Applications/QGIS 3.32.app',
    '/Applications/QGIS 3.30.app'
  ]

  for (const qgisPath of possiblePaths) {
    if (fs.existsSync(qgisPath)) {
      try {
        console.log(`尝试启动 QGIS: ${qgisPath}`)
        
        // 使用 open 命令启动应用
        const child = spawn('open', [qgisPath], {
          detached: true,
          stdio: 'ignore'
        })
        
        child.unref()
        
        return {
          success: true,
          path: qgisPath
        }
      } catch (error) {
        console.warn(`启动失败 ${qgisPath}:`, error.message)
        continue
      }
    }
  }

  throw new Error('未找到 QGIS 安装路径')
}

/**
 * Linux 平台启动 QGIS
 */
async function launchQGISLinux() {
  const possibleCommands = [
    'qgis',
    'qgis3',
    '/usr/bin/qgis',
    '/usr/local/bin/qgis',
    'flatpak run org.qgis.qgis'
  ]

  for (const command of possibleCommands) {
    try {
      console.log(`尝试启动 QGIS: ${command}`)
      
      const child = spawn(command, [], {
        detached: true,
        stdio: 'ignore',
        shell: true
      })
      
      child.unref()
      
      return {
        success: true,
        command: command
      }
    } catch (error) {
      console.warn(`启动失败 ${command}:`, error.message)
      continue
    }
  }

  throw new Error('未找到 QGIS 命令')
}

/**
 * 从 Windows 注册表获取 QGIS 路径
 */
async function getQGISPathFromRegistry() {
  return new Promise((resolve, reject) => {
    const regQuery = 'reg query "HKEY_LOCAL_MACHINE\\SOFTWARE\\QGIS" /s'
    
    exec(regQuery, (error, stdout, stderr) => {
      if (error) {
        reject(error)
        return
      }
      
      // 解析注册表输出，查找 QGIS 安装路径
      const lines = stdout.split('\n')
      for (const line of lines) {
        if (line.includes('InstallPath')) {
          const match = line.match(/REG_SZ\s+(.+)/)
          if (match) {
            const installPath = match[1].trim()
            const qgisExe = path.join(installPath, 'bin', 'qgis.exe')
            resolve(qgisExe)
            return
          }
        }
      }
      
      reject(new Error('未在注册表中找到 QGIS 路径'))
    })
  })
}

/**
 * 检查 QGIS 是否已安装
 */
async function checkQGISInstallation() {
  const platform = os.platform()
  
  try {
    switch (platform) {
      case 'win32':
        return await checkQGISWindows()
      case 'darwin':
        return await checkQGISMacOS()
      case 'linux':
        return await checkQGISLinux()
      default:
        return false
    }
  } catch (error) {
    console.error('检查 QGIS 安装状态失败:', error)
    return false
  }
}

/**
 * 检查 Windows 上的 QGIS 安装
 */
async function checkQGISWindows() {
  const possiblePaths = [
    'C:\\Program Files\\QGIS 3.34\\bin\\qgis.exe',
    'C:\\Program Files\\QGIS 3.32\\bin\\qgis.exe',
    'C:\\Program Files\\QGIS 3.30\\bin\\qgis.exe',
    'C:\\OSGeo4W64\\bin\\qgis.exe'
  ]

  return possiblePaths.some(path => fs.existsSync(path))
}

/**
 * 检查 macOS 上的 QGIS 安装
 */
async function checkQGISMacOS() {
  const possiblePaths = [
    '/Applications/QGIS.app',
    '/Applications/QGIS-LTR.app'
  ]

  return possiblePaths.some(path => fs.existsSync(path))
}

/**
 * 检查 Linux 上的 QGIS 安装
 */
async function checkQGISLinux() {
  return new Promise((resolve) => {
    exec('which qgis', (error) => {
      resolve(!error)
    })
  })
}

module.exports = {
  registerQGISHandlers,
  launchQGIS,
  checkQGISInstallation
}
