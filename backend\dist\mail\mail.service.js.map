{"version": 3, "file": "mail.service.js", "sourceRoot": "", "sources": ["../../src/mail/mail.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2DAAwD;AAExD,yCAAyC;AAGlC,IAAM,WAAW,mBAAjB,MAAM,WAAW;IAKO;IAJZ,MAAM,GAAG,IAAI,eAAM,CAAC,aAAW,CAAC,IAAI,CAAC,CAAC;IAC/C,WAAW,CAAyB;IACpC,KAAK,CAAQ;IAErB,YAA6B,YAA0B;QAA1B,iBAAY,GAAZ,YAAY,CAAc;QACrD,IAAI,CAAC;YAEH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAGhC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAC1C,MAAM,KAAK,CAAC;QACd,CAAC;QAGD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAC9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;YAGhF,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,aAAa,CAAC;YACxD,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,KAAK,CAAC,CAAC;YAE1D,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,eAAe,CAAC;gBAC5C,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,iBAAiB;gBAChD,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,KAAK,CAAC;gBAC9C,MAAM,EAAE,IAAI;gBACZ,IAAI,EAAE;oBACJ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,iBAAiB;oBAChD,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,aAAa;iBAC7C;gBACD,GAAG,EAAE;oBACH,kBAAkB,EAAE,KAAK;iBAC1B;gBACD,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;gBACZ,iBAAiB,EAAE,KAAK;gBACxB,eAAe,EAAE,KAAK;gBACtB,aAAa,EAAE,KAAK;aACrB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAG/B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YACpC,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAIO,wBAAwB;QAC9B,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C,CAAC;IAKD,KAAK,CAAC,oBAAoB,CAAC,KAAa,EAAE,OAAuC,OAAO;QACtF,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAC7C,MAAM,QAAQ,GAAG,cAAc,IAAI,IAAI,KAAK,EAAE,CAAC;YAC/C,MAAM,YAAY,GAAG,GAAG,QAAQ,OAAO,CAAC;YAExC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,KAAK,SAAS,IAAI,EAAE,CAAC,CAAC;YAG1D,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;gBACpD,IAAI,QAAQ,EAAE,CAAC;oBACb,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;gBACvC,CAAC;YACH,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;YAE/C,CAAC;YAGD,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;gBAC5C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,QAAQ,MAAM,IAAI,EAAE,CAAC,CAAC;gBAG1D,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;gBAC9C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,YAAY,EAAE,CAAC,CAAC;YAChD,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;gBAC9C,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;YACnC,CAAC;YAGD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,IAAI,EAAE,CAAC,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;YAG1D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,IAAI,EAAE,CAAC,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;YAE1D,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG;oBAClB,IAAI,EAAE,aAAa,OAAO,CAAC,GAAG,CAAC,SAAS,GAAG;oBAC3C,EAAE,EAAE,KAAK;oBACT,OAAO,EAAE,gBAAgB;oBACzB,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC;iBACxC,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,KAAK,EAAE,CAAC,CAAC;gBACxC,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBAC7C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,KAAK,EAAE,CAAC,CAAC;YAC7C,CAAC;YAAC,OAAO,SAAS,EAAE,CAAC;gBACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;gBAC1C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,IAAI,EAAE,CAAC,CAAC;YAEhD,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,IAAI,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAID,KAAK,CAAC,UAAU,CAAC,KAAa,EAAE,IAAY,EAAE,OAAuC,OAAO;QAC1F,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,cAAc,IAAI,IAAI,KAAK,EAAE,CAAC;YAE/C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,KAAK,UAAU,IAAI,SAAS,IAAI,EAAE,CAAC,CAAC;YACxE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,QAAQ,EAAE,CAAC,CAAC;YAE5C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAClD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,UAAU,EAAE,CAAC,CAAC;YAEjD,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,QAAQ,EAAE,CAAC,CAAC;gBAClD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;gBACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,KAAK,EAAE,CAAC,CAAC;gBAG5C,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBAC/B,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,OAAO,CAAC,CAAC;oBACzC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;gBAChC,CAAC;gBAAC,OAAO,QAAQ,EAAE,CAAC;oBAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;gBACzC,CAAC;gBAED,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,IAAI,SAAS,UAAU,EAAE,CAAC,CAAC;gBAC9D,OAAO,KAAK,CAAC;YACf,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,IAAI,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKO,gBAAgB,CAAC,IAAY,EAAE,IAAY;QACjD,MAAM,QAAQ,GAAG;YACf,KAAK,EAAE,IAAI;YACX,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,MAAM;SACd,CAAC;QAEF,OAAO;;;;;;;;;;;;;;;;4BAgBiB,QAAQ,CAAC,IAAI,CAAC;;;;qGAI2D,IAAI;;;;;;;;;;;;;;;;;;KAkBpG,CAAC;IACJ,CAAC;CACF,CAAA;AAtPY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAMgC,2BAAY;GAL5C,WAAW,CAsPvB"}