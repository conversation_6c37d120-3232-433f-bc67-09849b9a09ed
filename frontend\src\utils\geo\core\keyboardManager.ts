/**
 * 键盘快捷键管理器
 * 负责处理地理编辑相关的键盘快捷键
 */

export interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  shiftKey?: boolean
  altKey?: boolean
  description: string
  action: () => void
  enabled?: boolean
}

export class KeyboardManager {
  private shortcuts: Map<string, KeyboardShortcut> = new Map()
  private isActive = false
  private boundHandler: ((event: KeyboardEvent) => void) | null = null

  constructor() {
    this.boundHandler = this.handleKeyDown.bind(this)
  }

  /**
   * 激活键盘管理器
   */
  activate() {
    if (this.isActive) return
    
    this.isActive = true
    document.addEventListener('keydown', this.boundHandler!, true)
    console.log('⌨️ 键盘快捷键管理器已激活')
  }

  /**
   * 停用键盘管理器
   */
  deactivate() {
    if (!this.isActive) return
    
    this.isActive = false
    if (this.boundHandler) {
      document.removeEventListener('keydown', this.boundHandler, true)
    }
    console.log('⌨️ 键盘快捷键管理器已停用')
  }

  /**
   * 注册快捷键
   */
  registerShortcut(shortcut: KeyboardShortcut) {
    const key = this.getShortcutKey(shortcut)
    this.shortcuts.set(key, shortcut)
    console.log(`⌨️ 注册快捷键: ${this.getShortcutDisplay(shortcut)} - ${shortcut.description}`)
  }

  /**
   * 注销快捷键
   */
  unregisterShortcut(key: string, ctrlKey = false, shiftKey = false, altKey = false) {
    const shortcutKey = this.getShortcutKeyFromParams(key, ctrlKey, shiftKey, altKey)
    const removed = this.shortcuts.delete(shortcutKey)
    if (removed) {
      console.log(`⌨️ 注销快捷键: ${shortcutKey}`)
    }
    return removed
  }

  /**
   * 清除所有快捷键
   */
  clearShortcuts() {
    this.shortcuts.clear()
    console.log('⌨️ 清除所有快捷键')
  }

  /**
   * 获取所有已注册的快捷键
   */
  getShortcuts(): KeyboardShortcut[] {
    return Array.from(this.shortcuts.values())
  }

  /**
   * 处理键盘事件
   */
  private handleKeyDown(event: KeyboardEvent) {
    console.log('🎹 键盘事件:', event.key, '管理器激活状态:', this.isActive)

    // 如果焦点在输入框中，不处理快捷键
    const target = event.target as HTMLElement
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
      console.log('🎹 跳过输入框中的键盘事件')
      return
    }

    const shortcutKey = this.getShortcutKeyFromEvent(event)
    console.log('🎹 生成的快捷键:', shortcutKey)
    console.log('🎹 已注册的快捷键:', Array.from(this.shortcuts.keys()))

    const shortcut = this.shortcuts.get(shortcutKey)
    console.log('🎹 找到的快捷键:', shortcut)

    if (shortcut && (shortcut.enabled !== false)) {
      console.log(`⌨️ 触发快捷键: ${this.getShortcutDisplay(shortcut)}`)
      event.preventDefault()
      event.stopPropagation()

      try {
        shortcut.action()
      } catch (error) {
        console.error('⌨️ 快捷键执行失败:', error)
      }
    } else {
      console.log('🎹 未找到匹配的快捷键或快捷键被禁用')
    }
  }

  /**
   * 从快捷键对象生成键值
   */
  private getShortcutKey(shortcut: KeyboardShortcut): string {
    return this.getShortcutKeyFromParams(
      shortcut.key,
      shortcut.ctrlKey || false,
      shortcut.shiftKey || false,
      shortcut.altKey || false
    )
  }

  /**
   * 从参数生成键值
   */
  private getShortcutKeyFromParams(key: string, ctrlKey: boolean, shiftKey: boolean, altKey: boolean): string {
    const modifiers = []
    if (ctrlKey) modifiers.push('Ctrl')
    if (shiftKey) modifiers.push('Shift')
    if (altKey) modifiers.push('Alt')

    // 特殊键保持原样，字母键转小写
    const normalizedKey = ['Delete', 'Escape', 'Enter', 'Tab', 'Space', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(key)
      ? key
      : key.toLowerCase()

    return `${modifiers.join('+')}${modifiers.length > 0 ? '+' : ''}${normalizedKey}`
  }

  /**
   * 从键盘事件生成键值
   */
  private getShortcutKeyFromEvent(event: KeyboardEvent): string {
    return this.getShortcutKeyFromParams(
      event.key,
      event.ctrlKey,
      event.shiftKey,
      event.altKey
    )
  }

  /**
   * 获取快捷键的显示文本
   */
  private getShortcutDisplay(shortcut: KeyboardShortcut): string {
    const modifiers = []
    if (shortcut.ctrlKey) modifiers.push('Ctrl')
    if (shortcut.shiftKey) modifiers.push('Shift')
    if (shortcut.altKey) modifiers.push('Alt')

    const key = shortcut.key === ' ' ? 'Space' : shortcut.key
    return `${modifiers.join('+')}${modifiers.length > 0 ? '+' : ''}${key}`
  }

  /**
   * 获取所有快捷键
   */
  getShortcuts(): Map<string, KeyboardShortcut> {
    return this.shortcuts
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.deactivate()
    this.clearShortcuts()
    this.boundHandler = null
    console.log('⌨️ 键盘快捷键管理器已销毁')
  }
}

// 创建全局实例
export const globalKeyboardManager = new KeyboardManager()
