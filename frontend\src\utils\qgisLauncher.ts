/**
 * QGIS 启动器工具
 * 支持在不同环境下启动 QGIS 应用程序
 */

export interface QGISLaunchResult {
  success: boolean
  error?: string
  method?: string
}

/**
 * 启动 QGIS 应用程序
 */
export async function launchQGIS(): Promise<QGISLaunchResult> {
  console.log('🗺️ 开始启动 QGIS...')
  
  try {
    // 检查是否在 Electron 环境中
    if (window.electronAPI) {
      console.log('🖥️ 检测到 Electron 环境，使用 IPC 调用')
      return await launchQGISViaElectron()
    } else {
      console.log('🌐 检测到浏览器环境，使用协议启动')
      return await launchQGISViaBrowser()
    }
  } catch (error) {
    console.error('❌ 启动 QGIS 时发生错误:', error)
    return {
      success: false,
      error: `启动失败: ${error}`,
      method: 'unknown'
    }
  }
}

/**
 * 通过 Electron IPC 启动 QGIS
 */
async function launchQGISViaElectron(): Promise<QGISLaunchResult> {
  try {
    const result = await window.electronAPI!.openQGIS()
    return {
      ...result,
      method: 'electron'
    }
  } catch (error) {
    return {
      success: false,
      error: `Electron IPC 调用失败: ${error}`,
      method: 'electron'
    }
  }
}

/**
 * 通过浏览器协议启动 QGIS
 */
async function launchQGISViaBrowser(): Promise<QGISLaunchResult> {
  console.log('🌐 在浏览器环境中尝试启动 QGIS...')

  // 检测操作系统
  const os = getOperatingSystem()
  console.log(`🖥️ 检测到操作系统: ${os}`)

  // 由于浏览器安全限制，协议启动通常不可靠
  // 直接提供手动启动指导，这是最可靠的方式
  console.log('ℹ️ 由于浏览器安全限制，建议手动启动 QGIS')

  return {
    success: false,
    error: 'browser_limitation',
    method: 'browser-manual'
  }
}



/**
 * 检查 QGIS 是否已安装
 */
export function checkQGISInstallation(): Promise<boolean> {
  return new Promise((resolve) => {
    // 在浏览器环境中，我们无法直接检查软件安装
    // 这里返回一个假设值，实际检查需要在 Electron 环境中进行
    if (window.electronAPI) {
      // 如果有 Electron API，可以添加检查方法
      resolve(true) // 暂时返回 true
    } else {
      // 浏览器环境中无法检查
      resolve(false)
    }
  })
}

/**
 * 获取 QGIS 安装指南
 */
export function getQGISInstallGuide(): string {
  return `
QGIS 安装和使用指南：

📥 1. 下载安装 QGIS
   • 访问官网：https://qgis.org/
   • 推荐下载 LTR (长期支持) 版本
   • 按照安装向导完成安装

🚀 2. 手动启动 QGIS
   • Windows: 开始菜单 → QGIS → QGIS Desktop
   • macOS: 应用程序文件夹 → QGIS.app
   • Linux: 应用程序菜单 → QGIS 或命令行输入 qgis

📂 3. 导入地理数据
   • 打开 QGIS 后，点击"图层"菜单
   • 选择"添加图层" → "添加矢量图层"
   • 浏览并选择您的地理数据文件

🛠️ 4. 高级编辑功能
   • 右键点击图层 → "切换编辑"
   • 使用工具栏中的编辑工具
   • 完成后点击"保存图层编辑"

💡 提示：
   • 支持 Shapefile、GeoJSON、KML 等格式
   • 可以直接编辑几何形状和属性
   • 提供丰富的空间分析工具
  `.trim()
}

/**
 * 显示 QGIS 安装指南对话框
 */
export function showQGISInstallGuide(): void {
  const guide = getQGISInstallGuide()

  // 简化显示，只显示指南内容
  alert(guide)
}

/**
 * 获取操作系统类型
 */
export function getOperatingSystem(): 'windows' | 'macos' | 'linux' | 'unknown' {
  const userAgent = navigator.userAgent.toLowerCase()
  
  if (userAgent.includes('win')) {
    return 'windows'
  } else if (userAgent.includes('mac')) {
    return 'macos'
  } else if (userAgent.includes('linux')) {
    return 'linux'
  } else {
    return 'unknown'
  }
}

/**
 * 获取针对特定操作系统的安装建议
 */
export function getOSSpecificInstallGuide(): string {
  const os = getOperatingSystem()
  
  switch (os) {
    case 'windows':
      return `
Windows 用户安装指南：
1. 访问 https://qgis.org/en/site/forusers/download.html
2. 下载 "QGIS Standalone Installer" (推荐 LTR 版本)
3. 以管理员身份运行安装程序
4. 安装完成后重启浏览器
5. 如果仍无法启动，请检查防火墙设置
      `
    
    case 'macos':
      return `
macOS 用户安装指南：
1. 访问 https://qgis.org/en/site/forusers/download.html
2. 下载 macOS 版本的 QGIS
3. 拖拽到 Applications 文件夹
4. 首次运行时右键点击选择"打开"
5. 在系统偏好设置中允许运行
      `
    
    case 'linux':
      return `
Linux 用户安装指南：
1. Ubuntu/Debian: sudo apt update && sudo apt install qgis
2. Fedora: sudo dnf install qgis
3. 或使用 Flatpak: flatpak install flathub org.qgis.qgis
4. 安装完成后可能需要重新登录
      `
    
    default:
      return getQGISInstallGuide()
  }
}
