/**
 * QGIS 启动器工具
 * 支持在不同环境下启动 QGIS 应用程序
 */

export interface QGISLaunchResult {
  success: boolean
  error?: string
  method?: string
}

/**
 * 启动 QGIS 应用程序
 */
export async function launchQGIS(): Promise<QGISLaunchResult> {
  console.log('🗺️ 开始启动 QGIS...')
  
  try {
    // 检查是否在 Electron 环境中
    if (window.electronAPI) {
      console.log('🖥️ 检测到 Electron 环境，使用 IPC 调用')
      return await launchQGISViaElectron()
    } else {
      console.log('🌐 检测到浏览器环境，使用协议启动')
      return await launchQGISViaBrowser()
    }
  } catch (error) {
    console.error('❌ 启动 QGIS 时发生错误:', error)
    return {
      success: false,
      error: `启动失败: ${error}`,
      method: 'unknown'
    }
  }
}

/**
 * 通过 Electron IPC 启动 QGIS
 */
async function launchQGISViaElectron(): Promise<QGISLaunchResult> {
  try {
    const result = await window.electronAPI!.openQGIS()
    return {
      ...result,
      method: 'electron'
    }
  } catch (error) {
    return {
      success: false,
      error: `Electron IPC 调用失败: ${error}`,
      method: 'electron'
    }
  }
}

/**
 * 通过浏览器协议启动 QGIS
 */
async function launchQGISViaBrowser(): Promise<QGISLaunchResult> {
  console.log('🌐 在浏览器环境中尝试启动 QGIS...')

  // 检测操作系统
  const os = getOperatingSystem()
  console.log(`🖥️ 检测到操作系统: ${os}`)

  try {
    // 尝试使用协议启动
    const protocolResult = await tryProtocolLaunch()

    if (protocolResult.attempted) {
      // 协议尝试完成，但由于浏览器限制，我们无法确定是否成功
      // 给用户一个选择的机会
      return {
        success: false, // 设为false以触发用户确认流程
        error: 'protocol_attempted',
        method: 'browser-protocol'
      }
    } else {
      // 协议启动失败，直接显示手动启动指南
      return {
        success: false,
        error: 'protocol_not_supported',
        method: 'browser-manual'
      }
    }
  } catch (error) {
    console.error('协议启动过程中发生错误:', error)
    return {
      success: false,
      error: `协议启动失败: ${error}`,
      method: 'browser-protocol'
    }
  }
}

/**
 * 尝试协议启动
 */
async function tryProtocolLaunch(): Promise<{ attempted: boolean; error?: string }> {
  const protocols = [
    'qgis://',
    'qgis-desktop://',
    'qgis3://'
  ]

  for (const protocol of protocols) {
    try {
      console.log(`🔗 尝试协议: ${protocol}`)

      // 使用更可靠的方式尝试协议启动
      const success = await attemptProtocolLaunch(protocol)

      if (success) {
        console.log(`✅ 协议 ${protocol} 启动成功`)
        return { attempted: true }
      }
    } catch (error) {
      console.warn(`⚠️ 协议 ${protocol} 启动失败:`, error)
      continue
    }
  }

  return { attempted: false, error: '所有协议都不可用' }
}

/**
 * 尝试单个协议启动
 */
function attemptProtocolLaunch(protocol: string): Promise<boolean> {
  return new Promise((resolve) => {
    try {
      // 方法1: 使用 window.location
      const originalLocation = window.location.href

      // 创建一个临时的 a 标签
      const link = document.createElement('a')
      link.href = protocol
      link.style.display = 'none'
      document.body.appendChild(link)

      // 尝试点击链接
      link.click()

      // 清理
      setTimeout(() => {
        document.body.removeChild(link)
      }, 100)

      // 由于浏览器安全限制，我们无法确定协议是否真正启动成功
      // 这里返回 true 表示尝试已完成
      resolve(true)

    } catch (error) {
      console.warn(`协议 ${protocol} 启动失败:`, error)
      resolve(false)
    }
  })
}

/**
 * 检查 QGIS 是否已安装
 */
export function checkQGISInstallation(): Promise<boolean> {
  return new Promise((resolve) => {
    // 在浏览器环境中，我们无法直接检查软件安装
    // 这里返回一个假设值，实际检查需要在 Electron 环境中进行
    if (window.electronAPI) {
      // 如果有 Electron API，可以添加检查方法
      resolve(true) // 暂时返回 true
    } else {
      // 浏览器环境中无法检查
      resolve(false)
    }
  })
}

/**
 * 获取 QGIS 安装指南
 */
export function getQGISInstallGuide(): string {
  return `
QGIS 安装和配置指南：

📥 1. 下载安装 QGIS
   • 访问官网：https://qgis.org/
   • 下载适合您操作系统的版本
   • 按照安装向导完成安装

🪟 2. Windows 用户配置
   • 确保 QGIS 安装在默认路径
   • 推荐安装路径：C:\\Program Files\\QGIS 3.x\\
   • 可能需要以管理员身份运行浏览器

🍎 3. macOS 用户配置
   • 安装后可能需要在"系统偏好设置 > 安全性与隐私"中允许运行
   • 确保 QGIS 在 Applications 文件夹中
   • 可能需要右键点击 QGIS 图标选择"打开"

🐧 4. Linux 用户配置
   • Ubuntu/Debian: sudo apt install qgis
   • Fedora: sudo dnf install qgis
   • 或使用 Flatpak: flatpak install qgis

🔧 5. 协议关联配置
   • Windows: 安装时通常会自动关联 qgis:// 协议
   • macOS: 可能需要手动关联协议
   • Linux: 根据发行版不同可能需要额外配置

❓ 6. 故障排除
   • 如果协议启动失败，请手动打开 QGIS
   • 检查防火墙和安全软件设置
   • 确保 QGIS 版本为 3.x 或更高

💡 提示：建议使用 QGIS LTR (长期支持) 版本以获得最佳稳定性。
  `.trim()
}

/**
 * 显示 QGIS 安装指南对话框
 */
export function showQGISInstallGuide(): void {
  const guide = getQGISInstallGuide()
  
  // 创建一个更友好的显示方式
  if (confirm(guide + '\n\n是否要在新窗口中打开 QGIS 官网？')) {
    window.open('https://qgis.org/en/site/forusers/download.html', '_blank')
  }
}

/**
 * 获取操作系统类型
 */
export function getOperatingSystem(): 'windows' | 'macos' | 'linux' | 'unknown' {
  const userAgent = navigator.userAgent.toLowerCase()
  
  if (userAgent.includes('win')) {
    return 'windows'
  } else if (userAgent.includes('mac')) {
    return 'macos'
  } else if (userAgent.includes('linux')) {
    return 'linux'
  } else {
    return 'unknown'
  }
}

/**
 * 获取针对特定操作系统的安装建议
 */
export function getOSSpecificInstallGuide(): string {
  const os = getOperatingSystem()
  
  switch (os) {
    case 'windows':
      return `
Windows 用户安装指南：
1. 访问 https://qgis.org/en/site/forusers/download.html
2. 下载 "QGIS Standalone Installer" (推荐 LTR 版本)
3. 以管理员身份运行安装程序
4. 安装完成后重启浏览器
5. 如果仍无法启动，请检查防火墙设置
      `
    
    case 'macos':
      return `
macOS 用户安装指南：
1. 访问 https://qgis.org/en/site/forusers/download.html
2. 下载 macOS 版本的 QGIS
3. 拖拽到 Applications 文件夹
4. 首次运行时右键点击选择"打开"
5. 在系统偏好设置中允许运行
      `
    
    case 'linux':
      return `
Linux 用户安装指南：
1. Ubuntu/Debian: sudo apt update && sudo apt install qgis
2. Fedora: sudo dnf install qgis
3. 或使用 Flatpak: flatpak install flathub org.qgis.qgis
4. 安装完成后可能需要重新登录
      `
    
    default:
      return getQGISInstallGuide()
  }
}
