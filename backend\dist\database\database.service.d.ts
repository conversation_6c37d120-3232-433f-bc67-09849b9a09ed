import { TestConnectionDto, QueryLayerDto, DatabaseTableInfo, ConnectionTestResult, LayerQueryResult } from './dto/database-connection.dto';
export declare class DatabaseService {
    private readonly logger;
    private connections;
    testConnection(testDto: TestConnectionDto): Promise<ConnectionTestResult>;
    getTables(testDto: TestConnectionDto): Promise<DatabaseTableInfo[]>;
    queryLayer(queryDto: QueryLayerDto, connectionConfig: TestConnectionDto): Promise<LayerQueryResult>;
    private getGeometryColumns;
    private parseTableName;
    private calculateBounds;
    getTableInfo(connectionConfig: TestConnectionDto, tableName: string): Promise<any>;
    private extractCoordinates;
}
