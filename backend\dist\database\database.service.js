"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var DatabaseService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = void 0;
const common_1 = require("@nestjs/common");
const pg_1 = require("pg");
let DatabaseService = DatabaseService_1 = class DatabaseService {
    logger = new common_1.Logger(DatabaseService_1.name);
    connections = new Map();
    async testConnection(testDto) {
        let client;
        let pool;
        try {
            pool = new pg_1.Pool({
                host: testDto.host,
                port: testDto.port,
                database: testDto.database,
                user: testDto.username,
                password: testDto.password,
                max: 1,
                connectionTimeoutMillis: 5000,
                idleTimeoutMillis: 5000,
            });
            client = await pool.connect();
            const versionResult = await client.query('SELECT version()');
            const version = versionResult.rows[0].version;
            let postgisVersion;
            try {
                const postgisResult = await client.query('SELECT PostGIS_Version()');
                postgisVersion = postgisResult.rows[0].postgis_version;
            }
            catch (error) {
                this.logger.warn('PostGIS extension not found or not accessible');
            }
            return {
                success: true,
                message: '数据库连接成功',
                version,
                postgisVersion,
            };
        }
        catch (error) {
            this.logger.error('Database connection test failed:', error);
            let errorMessage = '连接失败';
            if (error.code === 'ECONNREFUSED') {
                errorMessage = '连接被拒绝，请检查数据库服务是否启动和网络连接';
            }
            else if (error.code === 'ENOTFOUND') {
                errorMessage = '无法找到主机，请检查主机地址是否正确';
            }
            else if (error.code === 'ETIMEDOUT') {
                errorMessage = '连接超时，请检查网络连接和防火墙设置';
            }
            else if (error.message.includes('password authentication failed')) {
                errorMessage = '用户名或密码错误';
            }
            else if (error.message.includes('database') && error.message.includes('does not exist')) {
                errorMessage = '数据库不存在';
            }
            else {
                errorMessage = `连接失败: ${error.message}`;
            }
            return {
                success: false,
                message: errorMessage,
            };
        }
        finally {
            if (client) {
                client.release();
            }
            if (pool) {
                await pool.end();
            }
        }
    }
    async getTables(testDto) {
        let client;
        let pool;
        try {
            pool = new pg_1.Pool({
                host: testDto.host,
                port: testDto.port,
                database: testDto.database,
                user: testDto.username,
                password: testDto.password,
                max: 1,
                connectionTimeoutMillis: 10000,
            });
            client = await pool.connect();
            const tablesQuery = `
        SELECT 
          t.table_name,
          t.table_schema,
          t.table_type
        FROM information_schema.tables t
        WHERE t.table_schema NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
        ORDER BY t.table_schema, t.table_name
      `;
            const tablesResult = await client.query(tablesQuery);
            const tables = [];
            for (const row of tablesResult.rows) {
                const tableName = row.table_name;
                const schema = row.table_schema;
                const tableType = row.table_type === 'VIEW' ? 'view' : 'table';
                const geometryColumns = await this.getGeometryColumns(client, schema, tableName);
                if (geometryColumns.length > 0) {
                    let totalRows;
                    try {
                        const countResult = await client.query(`SELECT COUNT(*) as count FROM "${schema}"."${tableName}"`);
                        totalRows = parseInt(countResult.rows[0].count);
                    }
                    catch (error) {
                        this.logger.warn(`Failed to get row count for ${schema}.${tableName}:`, error.message);
                    }
                    tables.push({
                        name: tableName,
                        schema,
                        type: tableType,
                        geometryColumns,
                        totalRows,
                    });
                }
            }
            return tables;
        }
        catch (error) {
            this.logger.error('Failed to get tables:', error);
            throw new common_1.BadRequestException(`获取表列表失败: ${error.message}`);
        }
        finally {
            if (client) {
                client.release();
            }
            if (pool) {
                await pool.end();
            }
        }
    }
    async queryLayer(queryDto, connectionConfig) {
        let client;
        let pool;
        try {
            pool = new pg_1.Pool({
                host: connectionConfig.host,
                port: connectionConfig.port,
                database: connectionConfig.database,
                user: connectionConfig.username,
                password: connectionConfig.password,
                max: 1,
                connectionTimeoutMillis: 30000,
            });
            client = await pool.connect();
            const [schema, tableName] = this.parseTableName(queryDto.tableName);
            const geometryColumns = await this.getGeometryColumns(client, schema, tableName);
            if (geometryColumns.length === 0) {
                throw new common_1.BadRequestException('表中没有找到几何列');
            }
            const geometryColumn = queryDto.geometryColumn || geometryColumns[0].columnName;
            const whereClause = queryDto.whereClause ? `WHERE ${queryDto.whereClause}` : '';
            const limitClause = queryDto.limit ? `LIMIT ${queryDto.limit}` : '';
            const query = `
        SELECT 
          ST_AsGeoJSON(ST_Transform("${geometryColumn}", 4326)) as geometry,
          *
        FROM "${schema}"."${tableName}"
        ${whereClause}
        ${limitClause}
      `;
            this.logger.log(`Executing query: ${query}`);
            const result = await client.query(query);
            const features = result.rows.map(row => {
                const { geometry, ...properties } = row;
                delete properties[geometryColumn];
                return {
                    type: 'Feature',
                    geometry: JSON.parse(geometry),
                    properties,
                };
            });
            const geojson = {
                type: 'FeatureCollection',
                features,
            };
            let bounds;
            if (features.length > 0) {
                bounds = this.calculateBounds(features);
            }
            return {
                success: true,
                data: geojson,
                totalFeatures: features.length,
                bounds,
            };
        }
        catch (error) {
            this.logger.error('Failed to query layer:', error);
            return {
                success: false,
                error: `查询图层失败: ${error.message}`,
            };
        }
        finally {
            if (client) {
                client.release();
            }
            if (pool) {
                await pool.end();
            }
        }
    }
    async getGeometryColumns(client, schema, tableName) {
        try {
            const query = `
        SELECT 
          f_geometry_column as column_name,
          type as geometry_type,
          srid,
          coord_dimension as dimension
        FROM geometry_columns 
        WHERE f_table_schema = $1 AND f_table_name = $2
      `;
            const result = await client.query(query, [schema, tableName]);
            return result.rows.map(row => ({
                columnName: row.column_name,
                geometryType: row.geometry_type,
                srid: row.srid,
                dimension: row.dimension,
            }));
        }
        catch (error) {
            this.logger.warn('geometry_columns table not accessible, trying alternative method');
            try {
                const fallbackQuery = `
          SELECT column_name
          FROM information_schema.columns 
          WHERE table_schema = $1 
            AND table_name = $2 
            AND udt_name = 'geometry'
        `;
                const result = await client.query(fallbackQuery, [schema, tableName]);
                return result.rows.map(row => ({
                    columnName: row.column_name,
                    geometryType: 'UNKNOWN',
                    srid: 4326,
                    dimension: 2,
                }));
            }
            catch (fallbackError) {
                this.logger.warn('Failed to get geometry columns:', fallbackError.message);
                return [];
            }
        }
    }
    parseTableName(fullTableName) {
        const parts = fullTableName.split('.');
        if (parts.length === 2) {
            return [parts[0], parts[1]];
        }
        return ['public', fullTableName];
    }
    calculateBounds(features) {
        let minLng = Infinity;
        let minLat = Infinity;
        let maxLng = -Infinity;
        let maxLat = -Infinity;
        features.forEach(feature => {
            if (feature.geometry && feature.geometry.coordinates) {
                this.extractCoordinates(feature.geometry.coordinates).forEach(([lng, lat]) => {
                    minLng = Math.min(minLng, lng);
                    minLat = Math.min(minLat, lat);
                    maxLng = Math.max(maxLng, lng);
                    maxLat = Math.max(maxLat, lat);
                });
            }
        });
        return [minLng, minLat, maxLng, maxLat];
    }
    async getTableInfo(connectionConfig, tableName) {
        let client;
        let pool;
        try {
            pool = new pg_1.Pool({
                host: connectionConfig.host,
                port: connectionConfig.port,
                database: connectionConfig.database,
                user: connectionConfig.username,
                password: connectionConfig.password,
                max: 1,
                connectionTimeoutMillis: 10000,
            });
            client = await pool.connect();
            const [schema, table] = this.parseTableName(tableName);
            const tableInfoQuery = `
        SELECT
          t.table_name,
          t.table_schema,
          t.table_type,
          obj_description(c.oid) as table_comment
        FROM information_schema.tables t
        LEFT JOIN pg_class c ON c.relname = t.table_name
        LEFT JOIN pg_namespace n ON n.oid = c.relnamespace AND n.nspname = t.table_schema
        WHERE t.table_schema = $1 AND t.table_name = $2
      `;
            const tableInfo = await client.query(tableInfoQuery, [schema, table]);
            if (tableInfo.rows.length === 0) {
                throw new common_1.BadRequestException(`表 ${tableName} 不存在`);
            }
            const columnsQuery = `
        SELECT
          column_name,
          data_type,
          is_nullable,
          column_default,
          character_maximum_length,
          numeric_precision,
          numeric_scale
        FROM information_schema.columns
        WHERE table_schema = $1 AND table_name = $2
        ORDER BY ordinal_position
      `;
            const columns = await client.query(columnsQuery, [schema, table]);
            const geometryColumns = await this.getGeometryColumns(client, schema, table);
            let totalRows;
            try {
                const countResult = await client.query(`SELECT COUNT(*) as count FROM "${schema}"."${table}"`);
                totalRows = parseInt(countResult.rows[0].count);
            }
            catch (error) {
                this.logger.warn(`Failed to get row count for ${schema}.${table}:`, error.message);
            }
            return {
                table: tableInfo.rows[0],
                columns: columns.rows,
                geometryColumns,
                totalRows,
            };
        }
        catch (error) {
            this.logger.error('Failed to get table info:', error);
            throw new common_1.BadRequestException(`获取表信息失败: ${error.message}`);
        }
        finally {
            if (client) {
                client.release();
            }
            if (pool) {
                await pool.end();
            }
        }
    }
    extractCoordinates(coords) {
        if (typeof coords[0] === 'number') {
            return [coords];
        }
        const result = [];
        coords.forEach((coord) => {
            result.push(...this.extractCoordinates(coord));
        });
        return result;
    }
};
exports.DatabaseService = DatabaseService;
exports.DatabaseService = DatabaseService = DatabaseService_1 = __decorate([
    (0, common_1.Injectable)()
], DatabaseService);
//# sourceMappingURL=database.service.js.map