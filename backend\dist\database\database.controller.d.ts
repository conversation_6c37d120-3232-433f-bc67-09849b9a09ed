import { DatabaseService } from './database.service';
import { TestConnectionDto, QueryLayerDto, ConnectionTestResult, LayerQueryResult, DatabaseTableInfo } from './dto/database-connection.dto';
export declare class DatabaseController {
    private readonly databaseService;
    private readonly logger;
    constructor(databaseService: DatabaseService);
    testConnection(testDto: TestConnectionDto): Promise<ConnectionTestResult>;
    getTables(testDto: TestConnectionDto): Promise<DatabaseTableInfo[]>;
    queryLayer(body: {
        queryDto: QueryLayerDto;
        connectionConfig: TestConnectionDto;
    }): Promise<LayerQueryResult>;
    getTableInfo(body: {
        connectionConfig: TestConnectionDto;
        tableName: string;
    }): Promise<any>;
    previewLayer(body: {
        queryDto: QueryLayerDto;
        connectionConfig: TestConnectionDto;
    }): Promise<LayerQueryResult>;
}
