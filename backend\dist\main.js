"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const crypto = require('crypto');
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const helmet_1 = require("helmet");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.use((0, helmet_1.default)());
    app.enableCors();
    app.setGlobalPrefix('api');
    await app.listen(process.env.PORT || 3000, '0.0.0.0');
    console.log(`应用正在运行: http://localhost:${process.env.PORT || 3000}`);
}
bootstrap();
//# sourceMappingURL=main.js.map