import { Request, Response } from 'express';
import { AuthService, LoginResponse, RefreshTokenResponse } from './auth.service';
import { User } from '../entities/user.entity';
import { LoginDto, ChangePasswordDto, RefreshTokenDto, RegisterDto, SendEmailCodeDto, EmailLoginDto } from './dto/login.dto';
export declare class AuthController {
    private readonly authService;
    private readonly logger;
    constructor(authService: AuthService);
    login(loginDto: LoginDto, req: Request, res: Response): Promise<{
        message: string;
        data: LoginResponse;
    }>;
    refreshToken(refreshTokenDto: RefreshTokenDto, req: Request): Promise<{
        message: string;
        data: RefreshTokenResponse;
    }>;
    logout(req: Request, res: Response, user: User): Promise<{
        message: string;
    }>;
    logoutAllDevices(user: User, res: Response): Promise<{
        message: string;
    }>;
    changePassword(changePasswordDto: ChangePasswordDto, user: User, res: Response): Promise<{
        message: string;
    }>;
    getCurrentUser(user: User): Promise<{
        message: string;
        data: {
            id: string;
            username: string;
            email: string;
            fullName: string;
            roles: string[];
            permissions: string[];
            isActive: boolean;
            lastLoginAt?: Date;
            createdAt: Date;
        };
    }>;
    verifyToken(user: User): Promise<{
        message: string;
        data: {
            valid: boolean;
            user: {
                id: string;
                username: string;
                roles: string[];
            };
        };
    }>;
    resetPassword(username: string, body: {
        password: string;
    }): Promise<{
        message: string;
        data: {
            message: string;
        };
    }>;
    register(registerDto: RegisterDto): Promise<{
        message: string;
        data: {
            id: string;
            username: string;
            email: string;
            fullName: string;
        };
    }>;
    sendEmailCode(sendEmailCodeDto: SendEmailCodeDto): Promise<{
        message: string;
    }>;
    loginWithEmail(emailLoginDto: EmailLoginDto, req: Request, res: Response): Promise<{
        message: string;
        data: LoginResponse;
    }>;
    private getClientIpAddress;
    private getUserPermissions;
}
