"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LayerQueryResult = exports.ConnectionTestResult = exports.GeometryColumnInfo = exports.DatabaseTableInfo = exports.QueryLayerDto = exports.TestConnectionDto = exports.CreateDatabaseConnectionDto = exports.DatabaseType = void 0;
const class_validator_1 = require("class-validator");
var DatabaseType;
(function (DatabaseType) {
    DatabaseType["POSTGRESQL"] = "postgresql";
    DatabaseType["MYSQL"] = "mysql";
    DatabaseType["SQLITE"] = "sqlite";
})(DatabaseType || (exports.DatabaseType = DatabaseType = {}));
class CreateDatabaseConnectionDto {
    name;
    type;
    host;
    port;
    database;
    username;
    password;
    schema;
}
exports.CreateDatabaseConnectionDto = CreateDatabaseConnectionDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDatabaseConnectionDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(DatabaseType),
    __metadata("design:type", String)
], CreateDatabaseConnectionDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDatabaseConnectionDto.prototype, "host", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateDatabaseConnectionDto.prototype, "port", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDatabaseConnectionDto.prototype, "database", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDatabaseConnectionDto.prototype, "username", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDatabaseConnectionDto.prototype, "password", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDatabaseConnectionDto.prototype, "schema", void 0);
class TestConnectionDto {
    host;
    port;
    database;
    username;
    password;
    schema;
}
exports.TestConnectionDto = TestConnectionDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TestConnectionDto.prototype, "host", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], TestConnectionDto.prototype, "port", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TestConnectionDto.prototype, "database", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TestConnectionDto.prototype, "username", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TestConnectionDto.prototype, "password", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TestConnectionDto.prototype, "schema", void 0);
class QueryLayerDto {
    connectionId;
    tableName;
    geometryColumn;
    whereClause;
    limit;
}
exports.QueryLayerDto = QueryLayerDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryLayerDto.prototype, "connectionId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryLayerDto.prototype, "tableName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryLayerDto.prototype, "geometryColumn", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryLayerDto.prototype, "whereClause", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], QueryLayerDto.prototype, "limit", void 0);
class DatabaseTableInfo {
    name;
    schema;
    type;
    geometryColumns;
    totalRows;
}
exports.DatabaseTableInfo = DatabaseTableInfo;
class GeometryColumnInfo {
    columnName;
    geometryType;
    srid;
    dimension;
}
exports.GeometryColumnInfo = GeometryColumnInfo;
class ConnectionTestResult {
    success;
    message;
    version;
    postgisVersion;
}
exports.ConnectionTestResult = ConnectionTestResult;
class LayerQueryResult {
    success;
    data;
    totalFeatures;
    bounds;
    error;
}
exports.LayerQueryResult = LayerQueryResult;
//# sourceMappingURL=database-connection.dto.js.map