"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DatabaseController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseController = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("./database.service");
const database_connection_dto_1 = require("./dto/database-connection.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let DatabaseController = DatabaseController_1 = class DatabaseController {
    databaseService;
    logger = new common_1.Logger(DatabaseController_1.name);
    constructor(databaseService) {
        this.databaseService = databaseService;
    }
    async testConnection(testDto) {
        this.logger.log(`Testing database connection to ${testDto.host}:${testDto.port}/${testDto.database}`);
        return this.databaseService.testConnection(testDto);
    }
    async getTables(testDto) {
        this.logger.log(`Getting tables from ${testDto.host}:${testDto.port}/${testDto.database}`);
        return this.databaseService.getTables(testDto);
    }
    async queryLayer(body) {
        this.logger.log(`Querying layer ${body.queryDto.tableName} from database`);
        return this.databaseService.queryLayer(body.queryDto, body.connectionConfig);
    }
    async getTableInfo(body) {
        this.logger.log(`Getting table info for ${body.tableName}`);
        return this.databaseService.getTableInfo(body.connectionConfig, body.tableName);
    }
    async previewLayer(body) {
        const previewQuery = { ...body.queryDto, limit: Math.min(body.queryDto.limit || 100, 100) };
        this.logger.log(`Previewing layer ${previewQuery.tableName} (max 100 features)`);
        return this.databaseService.queryLayer(previewQuery, body.connectionConfig);
    }
};
exports.DatabaseController = DatabaseController;
__decorate([
    (0, common_1.Post)('test-connection'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [database_connection_dto_1.TestConnectionDto]),
    __metadata("design:returntype", Promise)
], DatabaseController.prototype, "testConnection", null);
__decorate([
    (0, common_1.Post)('tables'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [database_connection_dto_1.TestConnectionDto]),
    __metadata("design:returntype", Promise)
], DatabaseController.prototype, "getTables", null);
__decorate([
    (0, common_1.Post)('query-layer'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DatabaseController.prototype, "queryLayer", null);
__decorate([
    (0, common_1.Post)('table-info'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DatabaseController.prototype, "getTableInfo", null);
__decorate([
    (0, common_1.Post)('preview-layer'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DatabaseController.prototype, "previewLayer", null);
exports.DatabaseController = DatabaseController = DatabaseController_1 = __decorate([
    (0, common_1.Controller)('database'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [database_service_1.DatabaseService])
], DatabaseController);
//# sourceMappingURL=database.controller.js.map