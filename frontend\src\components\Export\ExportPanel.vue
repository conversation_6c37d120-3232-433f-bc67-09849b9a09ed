<template>
  <div class="export-panel">
    <h3>导出数据</h3>
    
    <!-- 导出选项 -->
    <div class="export-options">
      <div class="format-selection">
        <h4>导出格式</h4>
        <div class="format-buttons">
          <button 
            v-for="format in exportFormats" 
            :key="format.key"
            class="format-btn"
            :class="{ active: selectedFormat === format.key }"
            @click="selectedFormat = format.key"
          >
            <span class="format-icon">{{ format.icon }}</span>
            <span class="format-name">{{ format.name }}</span>
            <span class="format-ext">.{{ format.extension }}</span>
          </button>
        </div>
      </div>

      <!-- 导出范围选择 -->
      <div class="export-scope">
        <h4>导出范围</h4>
        <div class="scope-options">
          <label class="scope-option">
            <input 
              type="radio" 
              v-model="exportScope" 
              value="all"
            >
            所有要素 ({{ totalFeatures }} 个)
          </label>
          <label class="scope-option">
            <input 
              type="radio" 
              v-model="exportScope" 
              value="selected"
              :disabled="selectedFeatures.length === 0"
            >
            选中要素 ({{ selectedFeatures.length }} 个)
          </label>
          <label class="scope-option">
            <input 
              type="radio" 
              v-model="exportScope" 
              value="visible"
            >
            可见要素
          </label>
        </div>
      </div>

      <!-- 文件名输入 -->
      <div class="filename-input">
        <label>文件名</label>
        <input 
          type="text" 
          v-model="filename" 
          placeholder="输入文件名"
          @input="validateFilename"
        >
        <span class="file-extension">.{{ currentFormat.extension }}</span>
      </div>

      <!-- 导出选项 -->
      <div class="export-settings" v-if="selectedFormat === 'geojson'">
        <h4>导出设置</h4>
        <label class="setting-option">
          <input type="checkbox" v-model="includeStyles">
          包含样式信息
        </label>
        <label class="setting-option">
          <input type="checkbox" v-model="prettyFormat">
          格式化输出 (更易读)
        </label>
      </div>
    </div>

    <!-- 导出按钮 -->
    <div class="export-actions">
      <button 
        class="export-btn primary"
        @click="performExport"
        :disabled="!canExport || isExporting"
      >
        <span v-if="isExporting" class="loading-icon">⏳</span>
        <span v-else class="export-icon">📁</span>
        {{ isExporting ? '导出中...' : '导出文件' }}
      </button>
      
      <button class="export-btn secondary" @click="previewData">
        <span class="preview-icon">👁️</span>
        预览数据
      </button>
    </div>

    <!-- 预览对话框 -->
    <div v-if="showPreview" class="preview-overlay" @click="closePreview">
      <div class="preview-dialog" @click.stop>
        <div class="preview-header">
          <h4>数据预览 - {{ currentFormat.name }}</h4>
          <button class="close-btn" @click="closePreview">×</button>
        </div>
        <div class="preview-content">
          <pre><code>{{ previewContent }}</code></pre>
        </div>
        <div class="preview-actions">
          <button class="copy-btn" @click="copyToClipboard">
            📋 复制到剪贴板
          </button>
        </div>
      </div>
    </div>

    <!-- 导出进度 -->
    <div v-if="exportProgress > 0" class="export-progress">
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: exportProgress + '%' }"></div>
      </div>
      <span class="progress-text">{{ exportProgress }}%</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { GeoManager, GeoFeature } from '@/utils/geo'

// Props
interface Props {
  geoManager?: GeoManager | null
  selectedFeatures?: GeoFeature[]
  visible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selectedFeatures: () => [],
  visible: true
})

const emit = defineEmits(['export-complete', 'close'])

// 导出格式配置
const exportFormats = [
  {
    key: 'geojson',
    name: 'GeoJSON',
    extension: 'geojson',
    icon: '🗺️',
    mimeType: 'application/geo+json'
  },
  {
    key: 'kml',
    name: 'KML',
    extension: 'kml',
    icon: '🌍',
    mimeType: 'application/vnd.google-earth.kml+xml'
  },
  {
    key: 'shp',
    name: 'Shapefile',
    extension: 'zip',
    icon: '📦',
    mimeType: 'application/zip'
  }
]

// 响应式数据
const selectedFormat = ref<string>('geojson')
const exportScope = ref<string>('all')
const filename = ref<string>('exported_data')
const includeStyles = ref<boolean>(true)
const prettyFormat = ref<boolean>(true)
const isExporting = ref<boolean>(false)
const exportProgress = ref<number>(0)
const showPreview = ref<boolean>(false)
const previewContent = ref<string>('')

// 计算属性
const currentFormat = computed(() => 
  exportFormats.find(f => f.key === selectedFormat.value) || exportFormats[0]
)

const totalFeatures = computed(() => {
  return props.geoManager?.getFeatures()?.length || 0
})

const canExport = computed(() => {
  return !isExporting.value && 
         filename.value.trim() !== '' && 
         totalFeatures.value > 0 &&
         (exportScope.value !== 'selected' || props.selectedFeatures.length > 0)
})

// 验证文件名
const validateFilename = () => {
  // 移除非法字符
  filename.value = filename.value.replace(/[<>:"/\\|?*]/g, '')
}

// 获取要导出的要素
const getFeaturesToExport = (): GeoFeature[] => {
  if (!props.geoManager) return []

  switch (exportScope.value) {
    case 'selected':
      return props.selectedFeatures
    case 'visible':
      // 这里可以添加可见性检查逻辑
      return props.geoManager.getFeatures()
    case 'all':
    default:
      return props.geoManager.getFeatures()
  }
}

// 导出功能
const performExport = async () => {
  if (!canExport.value || !props.geoManager) return

  isExporting.value = true
  exportProgress.value = 0

  try {
    const features = getFeaturesToExport()
    let exportData: string | Blob

    // 模拟进度
    exportProgress.value = 25

    switch (selectedFormat.value) {
      case 'geojson':
        exportData = exportToGeoJSON(features)
        break
      case 'kml':
        exportData = exportToKML(features)
        break
      case 'shp':
        exportData = await exportToShapefile(features)
        break
      default:
        throw new Error(`不支持的导出格式: ${selectedFormat.value}`)
    }

    exportProgress.value = 75

    // 下载文件
    await downloadFile(exportData, filename.value, currentFormat.value)
    
    exportProgress.value = 100

    // 完成后的清理
    setTimeout(() => {
      exportProgress.value = 0
      isExporting.value = false
      emit('export-complete', {
        format: selectedFormat.value,
        filename: filename.value,
        featuresCount: features.length
      })
    }, 1000)

  } catch (error) {
    console.error('导出失败:', error)
    alert(`导出失败: ${(error as Error).message}`)
    isExporting.value = false
    exportProgress.value = 0
  }
}

// 导出为GeoJSON
const exportToGeoJSON = (features: GeoFeature[]): string => {
  const geoJSON = {
    type: 'FeatureCollection',
    features: features.map(feature => ({
      type: 'Feature',
      id: feature.id,
      geometry: convertToGeoJSONGeometry(feature),      properties: {
        ...feature.properties,
        ...(includeStyles.value && (feature as any).style ? { style: (feature as any).style } : {}),
        timestamp: feature.timestamp?.toISOString()
      }
    }))
  }

  return prettyFormat.value 
    ? JSON.stringify(geoJSON, null, 2)
    : JSON.stringify(geoJSON)
}

// 导出为KML
const exportToKML = (features: GeoFeature[]): string => {
  let kml = `<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <name>${filename.value}</name>
    <description>从数字孪生保护地系统导出</description>
`

  features.forEach(feature => {
    kml += convertToKMLPlacemark(feature)
  })

  kml += `  </Document>
</kml>`

  return kml
}

// 导出为Shapefile (模拟)
const exportToShapefile = async (features: GeoFeature[]): Promise<Blob> => {
  // 这里应该实现真正的Shapefile导出逻辑
  // 由于Shapefile格式比较复杂，这里只是创建一个包含GeoJSON的ZIP文件
  const geoJSON = exportToGeoJSON(features)
  
  // 使用JSZip库创建ZIP文件 (需要安装 npm install jszip)
  const JSZip = (window as any).JSZip || require('jszip')
  if (!JSZip) {
    throw new Error('JSZip库未加载，无法导出Shapefile格式')
  }

  const zip = new JSZip()
  zip.file(`${filename.value}.geojson`, geoJSON)
  zip.file('README.txt', '此文件包含从数字孪生保护地系统导出的GeoJSON数据')
  
  return await zip.generateAsync({ type: 'blob' })
}

// 几何转换函数
const convertToGeoJSONGeometry = (feature: GeoFeature): any => {
  switch (feature.type) {
    case 'point':
      return {
        type: 'Point',
        coordinates: [feature.points[0].longitude, feature.points[0].latitude, feature.points[0].height || 0]
      }
    case 'line':
      return {
        type: 'LineString',
        coordinates: feature.points.map(p => [p.longitude, p.latitude, p.height || 0])
      }
    case 'polygon':
      return {
        type: 'Polygon',
        coordinates: [feature.points.map(p => [p.longitude, p.latitude, p.height || 0])]
      }
    default:
      throw new Error(`不支持的几何类型: ${feature.type}`)
  }
}

// KML转换函数
const convertToKMLPlacemark = (feature: GeoFeature): string => {
  let placemark = `    <Placemark>
      <name>${feature.properties?.name || feature.id}</name>
      <description>${feature.properties?.description || ''}</description>
`

  switch (feature.type) {
    case 'point':
      const point = feature.points[0]
      placemark += `      <Point>
        <coordinates>${point.longitude},${point.latitude},${point.height || 0}</coordinates>
      </Point>
`
      break
    case 'line':
      const lineCoords = feature.points
        .map(p => `${p.longitude},${p.latitude},${p.height || 0}`)
        .join(' ')
      placemark += `      <LineString>
        <coordinates>${lineCoords}</coordinates>
      </LineString>
`
      break
    case 'polygon':
      const polyCoords = feature.points
        .map(p => `${p.longitude},${p.latitude},${p.height || 0}`)
        .join(' ')
      placemark += `      <Polygon>
        <outerBoundaryIs>
          <LinearRing>
            <coordinates>${polyCoords}</coordinates>
          </LinearRing>
        </outerBoundaryIs>
      </Polygon>
`
      break
  }

  placemark += `    </Placemark>
`
  return placemark
}

// 下载文件
const downloadFile = async (data: string | Blob, filename: string, format: any) => {
  const blob = typeof data === 'string' 
    ? new Blob([data], { type: format.mimeType })
    : data

  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${filename}.${format.extension}`
  
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  // 清理URL对象
  URL.revokeObjectURL(url)
}

// 预览数据
const previewData = () => {
  if (!props.geoManager) return

  const features = getFeaturesToExport().slice(0, 5) // 只预览前5个要素
  
  switch (selectedFormat.value) {
    case 'geojson':
      previewContent.value = exportToGeoJSON(features)
      break
    case 'kml':
      previewContent.value = exportToKML(features)
      break
    case 'shp':
      previewContent.value = 'Shapefile 格式预览不可用，将导出为ZIP文件'
      break
  }
  
  showPreview.value = true
}

// 关闭预览
const closePreview = () => {
  showPreview.value = false
  previewContent.value = ''
}

// 复制到剪贴板
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(previewContent.value)
    alert('已复制到剪贴板！')
  } catch (error) {
    console.error('复制失败:', error)
    alert('复制失败，请手动选择文本复制')
  }
}

// 监听格式变化，自动更新文件扩展名
watch(selectedFormat, () => {
  // 移除旧的扩展名再加上新的
  const baseName = filename.value.replace(/\.[^/.]+$/, '')
  filename.value = baseName
})
</script>

<style scoped>
.export-panel {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  max-width: 600px;
}

.export-panel h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 16px;
}

.export-panel h4 {
  margin: 15px 0 10px 0;
  color: #555;
  font-size: 12px;
  font-weight: 600;
}

.format-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 10px;
  margin-bottom: 20px;
}

.format-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 10px;
  border: 2px solid #e0e6ed;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
}

.format-btn:hover {
  border-color: #007bff;
  transform: translateY(-1px);
}

.format-btn.active {
  border-color: #007bff;
  background: #f0f8ff;
}

.format-icon {
  font-size: 20px;
  margin-bottom: 5px;
}

.format-name {
  font-weight: 600;
  color: #333;
  font-size: 0.75rem;
}

.format-ext {
  font-size: 10px;
  color: #666;
}

.scope-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.scope-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.scope-option input[type="radio"] {
  margin: 0;
}

.filename-input {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.filename-input label {
  min-width: 60px;
  font-weight: 600;
  font-size: 0.75rem;
}

.filename-input input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.75rem;
}

.file-extension {
  color: #666;
  font-weight: 600;
  font-size: 0.75rem;
}

.export-settings {
  margin-bottom: 20px;
}

.setting-option {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  cursor: pointer;
}

.export-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.export-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
}

.export-btn.primary {
  background: #007bff;
  color: white;
}

.export-btn.primary:hover:not(:disabled) {
  background: #0056b3;
}

.export-btn.secondary {
  background: #6c757d;
  color: white;
}

.export-btn.secondary:hover {
  background: #545b62;
}

.export-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.export-progress {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #e0e6ed;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #007bff;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 10px;
  color: #666;
  min-width: 35px;
}

/* 预览对话框样式 */
.preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.preview-dialog {
  background: white;
  border-radius: 8px;
  max-width: 80vw;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.preview-header h4 {
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
}

.close-btn:hover {
  color: #333;
}

.preview-content {
  flex: 1;
  overflow: auto;
  padding: 20px;
}

.preview-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 10px;
  line-height: 1.4;
}

.preview-actions {
  padding: 20px;
  border-top: 1px solid #eee;
  text-align: right;
}

.copy-btn {
  padding: 8px 16px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.copy-btn:hover {
  background: #218838;
}
</style>
