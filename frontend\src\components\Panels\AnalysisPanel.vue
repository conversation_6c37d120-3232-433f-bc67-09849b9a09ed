<template>
  <BaseDraggablePanel
    title="空间分析"
    iconClass="fas fa-chart-area"
    panelClass="analysis-panel"
    :initialPosition="initialPanelPosition"
    @close="emit('close')"
  >

    <!-- 密度分析 -->
    <div class="tool-selection">
      <div class="section-title">密度分析</div>
      <div class="tool-grid">
        <el-button 
          type="primary"
          size="small"
          @click="startHeatmapAnalysis"
          :loading="analysisLoading.heatmap"
          class="tool-btn"
        >
          <i class="fas fa-fire"></i>
          <span>热点分析</span>
        </el-button>
        
        <el-button 
          size="small"
          @click="startDensityAnalysis"
          :loading="analysisLoading.density"
          class="tool-btn"
        >
          <i class="fas fa-chart-pie"></i>
          <span>点密度</span>
        </el-button>
      </div>
    </div>

    <!-- 视域分析 -->
    <div class="viewshed-section">
      <div class="section-title">视域分析</div>
      <div class="tool-grid">
        <el-button 
          type="success"
          size="small"
          @click="startViewshedAnalysis"
          :loading="analysisLoading.viewshed"
          class="tool-btn"
        >
          <i class="fas fa-eye"></i>
          <span>单点视域</span>
        </el-button>
        
        <el-button 
          size="small"
          @click="startMultiViewshed"
          :loading="analysisLoading.multiViewshed"
          class="tool-btn"
        >
          <i class="fas fa-th"></i>
          <span>多点视域</span>
        </el-button>
      </div>
    </div>

    <!-- 缓冲区分析 -->
    <div class="buffer-section">
      <div class="section-title">缓冲区分析</div>
      <div class="tool-grid">
        <el-button 
          size="small"
          @click="startBufferAnalysis"
          :loading="analysisLoading.buffer"
          class="tool-btn"
        >
          <i class="fas fa-circle-notch"></i>
          <span>缓冲区</span>
        </el-button>
        
        <el-button 
          size="small"
          @click="startMultiRingBuffer"
          :loading="analysisLoading.multiBuffer"
          class="tool-btn"
        >
          <i class="fas fa-bullseye"></i>
          <span>多环缓冲</span>
        </el-button>
      </div>
    </div>

    <!-- 分析参数设置 -->
    <div v-if="currentAnalysis" class="params-section">
      <div class="section-title">分析参数</div>
      
      <!-- 热力图参数 -->
      <div v-if="currentAnalysis === 'heatmap'" class="param-controls">
        <div class="control-group">
          <label>热点半径</label>
          <el-input-number
            v-model="heatmapParams.radius"
            :min="10"
            :max="1000"
            size="small"
            style="width: 100px"
          />
          <span class="unit">m</span>
        </div>
        
        <div class="control-group">
          <label>强度权重</label>
          <el-slider
            v-model="heatmapParams.intensity"
            :min="0.1"
            :max="2"
            :step="0.1"
            style="width: 120px"
          />
        </div>
        
        <div class="control-group">
          <label>颜色方案</label>
          <el-select v-model="heatmapParams.colorScheme" size="small" style="width: 100px">
            <el-option label="热力" value="heat" />
            <el-option label="彩虹" value="rainbow" />
            <el-option label="蓝绿" value="blue-green" />
          </el-select>
        </div>
      </div>

      <!-- 视域分析参数 -->
      <div v-if="currentAnalysis === 'viewshed'" class="param-controls">
        <div class="control-group">
          <label>观察高度</label>
          <el-input-number
            v-model="viewshedParams.observerHeight"
            :min="1"
            :max="100"
            size="small"
            style="width: 100px"
          />
          <span class="unit">m</span>
        </div>
        
        <div class="control-group">
          <label>观察距离</label>
          <el-input-number
            v-model="viewshedParams.viewDistance"
            :min="100"
            :max="10000"
            :step="100"
            size="small"
            style="width: 100px"
          />
          <span class="unit">m</span>
        </div>
        
        <div class="control-group">
          <label>水平角度</label>
          <el-input-number
            v-model="viewshedParams.horizontalAngle"
            :min="1"
            :max="360"
            size="small"
            style="width: 100px"
          />
          <span class="unit">°</span>
        </div>
      </div>

      <!-- 缓冲区参数 -->
      <div v-if="currentAnalysis === 'buffer'" class="param-controls">
        <div class="control-group">
          <label>缓冲距离</label>
          <el-input-number
            v-model="bufferParams.distance"
            :min="1"
            :max="10000"
            size="small"
            style="width: 100px"
          />
          <span class="unit">m</span>
        </div>
        
        <div class="control-group">
          <label>分段数</label>
          <el-input-number
            v-model="bufferParams.segments"
            :min="8"
            :max="64"
            size="small"
            style="width: 100px"
          />
        </div>
      </div>
    </div>

    <!-- 操作提示 -->
    <div v-if="currentAnalysis" class="analysis-tips">
      <el-alert
        :title="getAnalysisTip()"
        type="info"
        :closable="false"
        show-icon
      />
      <div class="tip-actions">
        <el-button 
          type="primary" 
          size="small" 
          @click="executeAnalysis"
          :loading="isAnalyzing"
        >
          <i class="fas fa-play"></i>
          执行分析
        </el-button>
        <el-button size="small" @click="stopAnalysis">
          <i class="fas fa-stop"></i>
          停止分析
        </el-button>
      </div>
    </div>

    <!-- 分析结果列表 -->
    <div class="results-section">
      <div class="section-header">
        <h4>分析结果</h4>
        <div class="header-actions">
          <el-button 
            size="small" 
            @click="showExportDialog = true"
            :disabled="analysisResults.length === 0"
          >
            <i class="fas fa-download"></i>
            导出
          </el-button>
          <el-button 
            size="small" 
            type="danger"
            @click="clearAllResults"
            :disabled="analysisResults.length === 0"
          >
            <i class="fas fa-trash"></i>
            清除
          </el-button>
        </div>
      </div>

      <div class="results-list">
        <div 
          v-for="result in analysisResults" 
          :key="result.id"
          class="result-item"
        >
          <div class="result-header">
            <div class="result-icon">
              <i :class="getResultIcon(result.type)"></i>
            </div>
            <div class="result-info">
              <div class="result-label">{{ result.type }}</div>
              <div class="result-time">{{ formatTime(result.timestamp) }}</div>
            </div>
            <div class="result-actions">
              <el-button 
                type="text" 
                size="small"
                @click="viewResult(result)"
                title="查看结果"
              >
                <i class="fas fa-eye"></i>
              </el-button>
              <el-button 
                type="text" 
                size="small"
                @click="deleteResult(result.id)"
                title="删除结果"
              >
                <i class="fas fa-trash"></i>
              </el-button>
            </div>
          </div>

          <!-- 详细信息 -->
          <div class="result-details">
            <div class="detail-item">
              <span class="detail-label">状态:</span>
              <span class="detail-value">{{ result.status }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">参数:</span>
              <span class="detail-value">{{ formatParams(result.params) }}</span>
            </div>
          </div>
        </div>

        <div v-if="analysisResults.length === 0" class="empty-state">
          <i class="fas fa-chart-area"></i>
          <p>暂无分析结果</p>
          <p class="empty-tip">选择分析工具开始分析</p>
        </div>
      </div>
    </div>

    <!-- 导出对话框 -->
    <el-dialog v-model="showExportDialog" title="导出分析结果" width="400px">
      <el-form :model="exportForm" label-width="80px">
        <el-form-item label="文件名">
          <el-input 
            v-model="exportForm.filename" 
            placeholder="请输入文件名"
          />
        </el-form-item>
        <el-form-item label="格式">
          <el-select v-model="exportForm.format" style="width: 100%">
            <el-option label="JSON" value="json" />
            <el-option label="GeoJSON" value="geojson" />
            <el-option label="CSV" value="csv" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showExportDialog = false">取消</el-button>        <el-button type="primary" @click="exportResults" :loading="exporting">
          导出
        </el-button>
      </template>
    </el-dialog>
  </BaseDraggablePanel>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BaseDraggablePanel from '../Common/BaseDraggablePanel.vue'

const emit = defineEmits<{
  close: []
  analysisStart: [type: string]
  analysisResult: [result: any]
}>()

// 面板初始位置
const initialPanelPosition = { x: 420, y: 100 }

// 分析状态
const currentAnalysis = ref('')
const isAnalyzing = ref(false)
const analysisLoading = ref({
  heatmap: false,
  density: false,
  viewshed: false,
  multiViewshed: false,
  buffer: false,
  multiBuffer: false
})

// 分析参数
const heatmapParams = ref({
  radius: 100,
  intensity: 1.0,
  colorScheme: 'heat'
})

const viewshedParams = ref({
  observerHeight: 10,
  viewDistance: 1000,
  horizontalAngle: 360
})

const bufferParams = ref({
  distance: 100,
  segments: 16
})

// 分析结果
const analysisResults = ref<any[]>([])

// 导出相关
const showExportDialog = ref(false)
const exporting = ref(false)
const exportForm = ref({
  filename: `analysis_results_${new Date().toISOString().slice(0, 10)}`,
  format: 'json'
})

// 分析功能
const getAnalysisTip = () => {
  switch (currentAnalysis.value) {
    case 'heatmap':
      return '请在地图上选择数据点进行热点分析'
    case 'viewshed':
      return '请在地图上点击选择观察点'
    case 'buffer':
      return '请在地图上选择要素进行缓冲区分析'
    default:
      return '请配置分析参数后执行分析'
  }
}

const getResultIcon = (type: string) => {
  switch (type) {
    case '热点分析': return 'fas fa-fire'
    case '点密度分析': return 'fas fa-chart-pie'
    case '视域分析': return 'fas fa-eye'
    case '缓冲区分析': return 'fas fa-circle-notch'
    default: return 'fas fa-chart-area'
  }
}

const formatTime = (timestamp: Date) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const formatParams = (params: any) => {
  if (!params) return '无'
  const keys = Object.keys(params)
  if (keys.length === 0) return '无'
  return keys.slice(0, 2).map(key => `${key}: ${params[key]}`).join(', ')
}

// 分析操作
const startHeatmapAnalysis = () => {
  currentAnalysis.value = 'heatmap'
  ElMessage.info('请在地图上选择数据点进行热点分析')
  emit('analysisStart', 'heatmap')
}

const startDensityAnalysis = () => {
  currentAnalysis.value = 'density'
  ElMessage.info('点密度分析功能开发中...')
}

const startViewshedAnalysis = () => {
  currentAnalysis.value = 'viewshed'
  ElMessage.info('请在地图上点击选择观察点')
  emit('analysisStart', 'viewshed')
}

const startMultiViewshed = () => {
  currentAnalysis.value = 'multiViewshed'
  ElMessage.info('多点视域分析功能开发中...')
}

const startBufferAnalysis = () => {
  currentAnalysis.value = 'buffer'
  ElMessage.info('请在地图上选择要素进行缓冲区分析')
  emit('analysisStart', 'buffer')
}

const startMultiRingBuffer = () => {
  currentAnalysis.value = 'multiBuffer'
  ElMessage.info('多环缓冲分析功能开发中...')
}

const executeAnalysis = async () => {
  if (!currentAnalysis.value) return

  isAnalyzing.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const result = {
      id: `${currentAnalysis.value}_${Date.now()}`,
      type: getAnalysisTypeName(currentAnalysis.value),
      timestamp: new Date(),
      params: getCurrentParams(),
      status: '已完成'
    }
    
    analysisResults.value.push(result)
    emit('analysisResult', result)
    
    ElMessage.success(`${result.type}分析完成！`)
    
  } catch (error) {
    ElMessage.error('分析执行失败')
  } finally {
    isAnalyzing.value = false
  }
}

const stopAnalysis = () => {
  currentAnalysis.value = ''
  isAnalyzing.value = false
  ElMessage.info('分析已停止')
}

const getCurrentParams = () => {
  switch (currentAnalysis.value) {
    case 'heatmap':
      return { ...heatmapParams.value }
    case 'viewshed':
      return { ...viewshedParams.value }
    case 'buffer':
      return { ...bufferParams.value }
    default:
      return {}
  }
}

const getAnalysisTypeName = (type: string) => {
  const names: Record<string, string> = {
    heatmap: '热点分析',
    density: '点密度分析',
    viewshed: '视域分析',
    multiViewshed: '多点视域',
    buffer: '缓冲区分析',
    multiBuffer: '多环缓冲'
  }
  return names[type] || type
}

const viewResult = (result: any) => {
  ElMessage.info(`查看分析结果: ${result.type}`)
}

const deleteResult = (resultId: string) => {
  const index = analysisResults.value.findIndex(r => r.id === resultId)
  if (index > -1) {
    analysisResults.value.splice(index, 1)
    ElMessage.success('分析结果已删除')
  }
}

const clearAllResults = () => {
  ElMessageBox.confirm(
    '确定要清除所有分析结果吗？',
    '清除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    analysisResults.value = []
    currentAnalysis.value = ''
    ElMessage.success('所有分析结果已清除')
    return true
  }).catch(() => {
    // 用户取消
    return false
  })
}

const exportResults = async () => {
  if (!exportForm.value.filename.trim()) {
    ElMessage.error('请输入文件名')
    return
  }

  exporting.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('导出成功')
    showExportDialog.value = false
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
  exporting.value = false
  }
}
</script>

<style scoped>
.analysis-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 400px;
  max-height: 85vh;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  pointer-events: auto; /* 确保面板本身可以交互 */
}

.panel-dragging {
  user-select: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  cursor: grab;
}

.panel-header:active {
  cursor: grabbing;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.close-btn {
  color: white !important;
  padding: 4px !important;
}

.tool-selection,
.viewshed-section,
.buffer-section,
.params-section {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.tool-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.tool-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  height: 60px;
  font-size: 10px;
}

.tool-btn i {
  font-size: 16px;
}

.param-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.control-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.control-group label {
  font-size: 10px;
  color: #6b7280;
  min-width: 80px;
}

.unit {
  color: #6b7280;
  font-size: 10px;
  margin-left: 8px;
}

.analysis-tips {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.tip-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.results-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 16px 8px 16px;
}

.section-header h4 {
  margin: 0;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.results-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px 16px 16px;
}

.result-item {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 12px;
  overflow: hidden;
}

.result-header {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f8fafc;
}

.result-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #8b5cf6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 12px;
}

.result-info {
  flex: 1;
}

.result-label {
  font-weight: 500;
  color: #1f2937;
  font-size: 12px;
}

.result-time {
  font-size: 10px;
  color: #6b7280;
  margin-top: 2px;
}

.result-actions {
  display: flex;
  gap: 4px;
}

.result-details {
  padding: 12px;
  background: white;
  border-top: 1px solid #e5e7eb;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 11px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #6b7280;
}

.detail-value {
  color: #1f2937;
  font-weight: 500;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
  font-size: 0.75rem;
}

.empty-state i {
  font-size: 40px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state p {
  margin: 8px 0;
}

.empty-tip {
  font-size: 10px;
  opacity: 0.7;
}

/* 滚动条样式 */
.results-list::-webkit-scrollbar {
  width: 6px;
}

.results-list::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.results-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.results-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
