import { User } from './user.entity';
export declare class Role {
    role_id: string;
    name: string;
    display_name: string;
    description: string;
    permissions: string[] | string;
    is_system: boolean;
    created_at: Date;
    updated_at: Date;
    users: User[];
    getPermissions(): string[];
    hasPermission(permission: string): boolean;
    addPermission(permission: string): void;
    removePermission(permission: string): void;
    toSafeObject(): {
        role_id: string;
        name: string;
        display_name: string;
        description: string;
        permissions: string[];
        is_system: boolean;
        created_at: Date;
        updated_at: Date;
        user_count: number;
    };
}
