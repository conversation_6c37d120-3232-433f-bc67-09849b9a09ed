<template>
  <div class="draw-panel-content">

    <!-- 绘图工具选择 -->
    <div class="tool-selection">
      <!-- 第一排：绘图工具 -->
      <div class="tool-grid">
        <el-button
          :type="activeDrawType === 'point' ? 'primary' : 'default'"
          size="small"
          @click="startDraw('point')"
          class="tool-btn"
          title="点"
        >
          <i class="fas fa-circle point-icon"></i>
        </el-button>

        <el-button
          :type="activeDrawType === 'line' ? 'primary' : 'default'"
          size="small"
          @click="startDraw('line')"
          class="tool-btn"
          title="线"
        >
          <i class="fas fa-minus"></i>
        </el-button>

        <el-button
          :type="activeDrawType === 'polygon' ? 'primary' : 'default'"
          size="small"
          @click="startDraw('polygon')"
          class="tool-btn"
          title="多边形"
        >
          <i class="fas fa-draw-polygon"></i>
        </el-button>

        <el-button
          :type="activeDrawType === 'circle' ? 'primary' : 'default'"
          size="small"
          @click="startDraw('circle')"
          class="tool-btn"
          title="圆形"
        >
          <i class="fas fa-circle"></i>
        </el-button>

        <el-button
          :type="activeDrawType === 'rectangle' ? 'primary' : 'default'"
          size="small"
          @click="startDraw('rectangle')"
          class="tool-btn"
          title="矩形"
        >
          <i class="fas fa-square"></i>
        </el-button>
      </div>

      <!-- 第二排：操作按钮 -->
      <div class="action-grid">
        <el-button
          size="small"
          @click="stopDraw"
          class="action-btn"
        >
          停止绘制
        </el-button>

        <el-button
          size="small"
          @click="clearAll"
          class="action-btn"
        >
          清除全部
        </el-button>
      </div>
    </div>

    <!-- 绘制提示 -->
    <div v-if="activeDrawType" class="draw-tips">
      <el-alert
        :title="getDrawTip()"
        type="info"
        :closable="false"
        show-icon
      />
    </div>





    <!-- 绘制结果列表 -->
    <div class="results-section">
      <div class="section-header">
        <h4>绘制结果</h4>
        <div class="header-actions">
          <el-button 
            size="small" 
            @click="showExportDialog = true"
            :disabled="drawResults.length === 0"
          >
            <i class="fas fa-download"></i>
            导出
          </el-button>
        </div>
      </div>

      <div class="results-list">
        <div
          v-for="layer in layerSummary"
          :key="layer.type"
          class="layer-item"
        >
          <div class="layer-header">
            <div class="layer-icon" :style="{ backgroundColor: getLayerColor(layer.type) }">
              <i :class="getResultIcon(layer.type)"></i>
            </div>
            <div class="layer-info">
              <div class="layer-label">{{ getTypeLabel(layer.type) }}图层</div>
              <div class="layer-count">{{ layer.count }}个要素</div>
            </div>
            <div class="layer-actions">
              <el-button
                type="text"
                size="small"
                @click="zoomToLayer(layer.type)"
                title="定位到图层"
                :disabled="layer.count === 0"
              >
                <i class="fas fa-search-location"></i>
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="toggleLayerVisibility(layer.type)"
                :title="layer.visible ? '隐藏图层' : '显示图层'"
                :disabled="layer.count === 0"
              >
                <i :class="layer.visible ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="showAttributeTable(layer.type)"
                title="属性表"
                :disabled="layer.count === 0"
              >
                <i class="fas fa-table"></i>
              </el-button>
            </div>
          </div>
        </div>


      </div>
    </div>

    <!-- 编辑对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑绘制对象" width="400px">
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="名称">
          <el-input v-model="editForm.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="editForm.description" 
            type="textarea"
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="颜色">
          <el-color-picker v-model="editForm.color" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="saveEdit">保存</el-button>
      </template>
    </el-dialog>

    <!-- 导出对话框 -->
    <el-dialog v-model="showExportDialog" title="导出绘制结果" width="400px">
      <el-form :model="exportForm" label-width="80px">
        <el-form-item label="文件名">
          <el-input
            v-model="exportForm.filename"
            placeholder="请输入文件名"
            suffix-icon="Document"
          />
        </el-form-item>
        <el-form-item label="格式">
          <el-select v-model="exportForm.format" style="width: 100%">
            <el-option label="JSON" value="json" />
            <el-option label="GeoJSON" value="geojson" />
            <el-option label="KML" value="kml" />
          </el-select>
        </el-form-item>
      </el-form>
        <template #footer>
        <el-button @click="showExportDialog = false">取消</el-button>
        <el-button type="primary" @click="exportResults" :loading="exporting">
          导出
        </el-button>
      </template>
    </el-dialog>

    <!-- 属性表对话框 -->
    <el-dialog
      v-model="showAttributeDialog"
      :title="`${getTypeLabel(currentLayerType)}图层属性表`"
      width="800px"
      :before-close="handleAttributeDialogClose"
    >
      <div class="attribute-table-container">
        <div class="table-header">
          <span>共 {{ currentLayerFeatures.length }} 个要素</span>
          <div class="header-actions">
            <el-button size="small" @click="addNewFeature" type="primary">
              <i class="fas fa-plus"></i>
              新增要素
            </el-button>
            <el-button size="small" @click="deleteSelectedFeatures" :disabled="selectedFeatures.length === 0">
              <i class="fas fa-trash"></i>
              删除选中
            </el-button>
          </div>
        </div>

        <el-table
          :data="currentLayerFeatures"
          style="width: 100%"
          max-height="400px"
          @selection-change="handleSelectionChange"
          stripe
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="ID" width="120" />
          <el-table-column prop="type" label="类型" width="80">
            <template #default="scope">
              {{ getTypeLabel(scope.row.type) }}
            </template>
          </el-table-column>
          <el-table-column prop="properties.name" label="名称" width="120">
            <template #default="scope">
              <el-input
                v-model="scope.row.properties.name"
                size="small"
                placeholder="未命名"
                @blur="updateFeatureProperty(scope.row, 'name', scope.row.properties?.name)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="properties.description" label="描述" min-width="150">
            <template #default="scope">
              <el-input
                v-model="scope.row.properties.description"
                size="small"
                placeholder="无描述"
                @blur="updateFeatureProperty(scope.row, 'description', scope.row.properties?.description)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="properties.color" label="颜色" width="100">
            <template #default="scope">
              <el-color-picker
                v-model="scope.row.properties.color"
                size="small"
                @change="updateFeatureProperty(scope.row, 'color', scope.row.properties?.color)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="timestamp" label="创建时间" width="150">
            <template #default="scope">
              {{ formatTime(scope.row.timestamp) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button
                type="text"
                size="small"
                @click="zoomToFeature(scope.row)"
                title="定位"
              >
                <i class="fas fa-search-location"></i>
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="deleteFeature(scope.row)"
                title="删除"
              >
                <i class="fas fa-trash"></i>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <el-button @click="showAttributeDialog = false">关闭</el-button>
        <el-button type="primary" @click="saveAttributeChanges">保存更改</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { DrawResult } from '../../utils/drawTools'

// 图层类型定义
interface LayerSummary {
  type: string
  count: number
  visible: boolean
}

const props = defineProps<{
  drawResults: DrawResult[]
  activeDrawType: string | null
}>()

const emit = defineEmits<{
  close: []
  startDraw: [type: 'point' | 'line' | 'polygon' | 'circle' | 'rectangle']
  stopDraw: []
  clearAll: []
  deleteResult: [id: string]
  zoomToResult: [result: DrawResult]
  exportResults: [filename: string, format: string]
  updateStyle: [style: any]
  editResult: [result: DrawResult]
  clearLayer: [type: string]
  zoomToLayer: [type: string]
  toggleLayerVisibility: [type: string, visible: boolean]
}>()



// 固定样式设置
const getDrawStyle = (type: string) => {
  switch (type) {
    case 'point':
      return {
        strokeColor: '#10b981', // 绿色
        fillColor: '#10b981',
        strokeWidth: 1,
        fillOpacity: 1
      }
    case 'line':
      return {
        strokeColor: '#fbbf24', // 黄色
        fillColor: '#fbbf24',
        strokeWidth: 1,
        fillOpacity: 1
      }
    case 'polygon':
    case 'circle':
    case 'rectangle':
      return {
        strokeColor: '#fbbf24', // 黄色轮廓
        fillColor: '#3b82f6',   // 蓝色填充
        strokeWidth: 1,
        fillOpacity: 0.5        // 50%透明度
      }
    default:
      return {
        strokeColor: '#3b82f6',
        fillColor: '#3b82f6',
        strokeWidth: 1,
        fillOpacity: 0.5
      }
  }
}

// 图层可见性状态
const layerVisibility = ref({
  point: true,
  line: true,
  polygon: true,
  circle: true,
  rectangle: true
})

// 计算图层摘要
const layerSummary = computed((): LayerSummary[] => {
  const types = ['point', 'line', 'polygon', 'circle', 'rectangle']
  return types.map(type => ({
    type,
    count: props.drawResults.filter(result => result.type === type).length,
    visible: layerVisibility.value[type as keyof typeof layerVisibility.value]
  }))
})

// 对话框
const showEditDialog = ref(false)
const showExportDialog = ref(false)
const showAttributeDialog = ref(false)
const exporting = ref(false)

// 属性表相关
const currentLayerType = ref('')
const currentLayerFeatures = ref<DrawResult[]>([])
const selectedFeatures = ref<DrawResult[]>([])

// 编辑表单
const editForm = ref({
  id: '',
  name: '',
  description: '',
  color: '#3b82f6'
})

// 导出表单
const exportForm = ref({
  filename: `draw_results_${new Date().toISOString().slice(0, 10)}`,
  format: 'json'
})





const getResultIcon = (type: string) => {
  switch (type) {
    case 'point': return 'fas fa-map-marker-alt'
    case 'line': return 'fas fa-minus'
    case 'polygon': return 'fas fa-draw-polygon'
    case 'circle': return 'fas fa-circle'
    case 'rectangle': return 'fas fa-square'
    default: return 'fas fa-draw-polygon'
  }
}

const getTypeLabel = (type: string) => {
  switch (type) {
    case 'point': return '点标记'
    case 'line': return '线条'
    case 'polygon': return '多边形'
    case 'circle': return '圆形'
    case 'rectangle': return '矩形'
    default: return '未知'
  }
}

const formatTime = (timestamp: number | string | Date) => {
  if (!timestamp) return '未知时间'
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 获取绘制提示
const getDrawTip = () => {
  switch (props.activeDrawType) {
    case 'point':
      return '单击地图添加点标记。支持连续添加多个点。'
    case 'line':
      return '单击地图添加线条顶点，右键完成当前线条绘制。支持连续绘制多条线。'
    case 'polygon':
      return '单击地图添加多边形顶点，右键闭合多边形。支持连续绘制多个多边形。'
    case 'circle':
      return '单击地图中心点，拖拽确定半径。支持连续绘制多个圆形。'
    case 'rectangle':
      return '单击地图一角，拖拽确定矩形大小。支持连续绘制多个矩形。'
    default:
      return ''
  }
}

// 样式更新
const updateStyle = (type: string) => {
  const style = getDrawStyle(type)
  emit('updateStyle', style)
}

// 绘图操作
const startDraw = (type: 'point' | 'line' | 'polygon' | 'circle' | 'rectangle') => {
  // 应用对应类型的固定样式
  updateStyle(type)
  emit('startDraw', type)
}

const stopDraw = () => {
  emit('stopDraw')
}

const clearAll = () => {
  emit('clearAll')
}

const deleteResult = (id: string) => {
  emit('deleteResult', id)
}

const zoomToResult = (result: DrawResult) => {
  emit('zoomToResult', result)
}

// 图层管理方法
const getLayerColor = (type: string) => {
  const colors = {
    point: '#3b82f6',
    line: '#10b981',
    polygon: '#f59e0b',
    circle: '#ef4444',
    rectangle: '#8b5cf6'
  }
  return colors[type as keyof typeof colors] || '#6b7280'
}

const clearLayer = (type: string) => {
  emit('clearLayer', type)
}

const zoomToLayer = (type: string) => {
  emit('zoomToLayer', type)
}

const toggleLayerVisibility = (type: string) => {
  layerVisibility.value[type as keyof typeof layerVisibility.value] =
    !layerVisibility.value[type as keyof typeof layerVisibility.value]
  emit('toggleLayerVisibility', type, layerVisibility.value[type as keyof typeof layerVisibility.value])
}

// 属性表相关方法
const showAttributeTable = (type: string) => {
  currentLayerType.value = type
  currentLayerFeatures.value = props.drawResults.filter(result => result.type === type)

  // 确保每个要素都有 properties 对象
  currentLayerFeatures.value.forEach(feature => {
    if (!feature.properties) {
      feature.properties = {
        name: '',
        description: '',
        color: getLayerColor(type)
      }
    }
  })

  selectedFeatures.value = []
  showAttributeDialog.value = true
}

const handleSelectionChange = (selection: DrawResult[]) => {
  selectedFeatures.value = selection
}

const updateFeatureProperty = (feature: DrawResult, property: string, value: any) => {
  if (!feature.properties) {
    feature.properties = {}
  }
  feature.properties[property] = value

  // 触发编辑事件
  emit('editResult', feature)
}

const zoomToFeature = (feature: DrawResult) => {
  emit('zoomToResult', feature)
}

const deleteFeature = (feature: DrawResult) => {
  // 从当前图层要素列表中移除
  const index = currentLayerFeatures.value.findIndex(f => f.id === feature.id)
  if (index > -1) {
    currentLayerFeatures.value.splice(index, 1)
  }

  // 触发删除事件
  emit('deleteResult', feature.id)

  ElMessage.success('要素已删除')
}

const deleteSelectedFeatures = () => {
  if (selectedFeatures.value.length === 0) return

  selectedFeatures.value.forEach(feature => {
    deleteFeature(feature)
  })

  selectedFeatures.value = []
  ElMessage.success(`已删除 ${selectedFeatures.value.length} 个要素`)
}

const addNewFeature = () => {
  ElMessage.info('请在地图上绘制新要素')
  showAttributeDialog.value = false
  // 触发开始绘制
  emit('startDraw', currentLayerType.value as any)
}

const saveAttributeChanges = () => {
  // 所有更改都是实时保存的，这里只是确认
  ElMessage.success('属性更改已保存')
  showAttributeDialog.value = false
}

const handleAttributeDialogClose = () => {
  showAttributeDialog.value = false
}

// 编辑功能
const editResult = (result: DrawResult) => {
  editForm.value = {
    id: result.id,
    name: result.properties?.name || '',
    description: result.properties?.description || '',
    color: result.properties?.color || '#3b82f6'
  }
  showEditDialog.value = true
}

const saveEdit = () => {
  const result = props.drawResults.find(r => r.id === editForm.value.id)
  if (result) {
    // 确保 properties 存在
    if (!result.properties) {
      result.properties = {};
    }
    
    result.properties.name = editForm.value.name
    result.properties.description = editForm.value.description
    result.properties.color = editForm.value.color
    
    emit('editResult', result)
    ElMessage.success('编辑保存成功')
  }
  showEditDialog.value = false
}

// 导出功能
const exportResults = async () => {
  if (!exportForm.value.filename.trim()) {
    ElMessage.error('请输入文件名')
    return
  }

  exporting.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('exportResults', exportForm.value.filename, exportForm.value.format)
    
    ElMessage.success('导出成功')
    showExportDialog.value = false
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}


</script>

<style scoped>
.draw-panel-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tool-selection {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.tool-selection {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.tool-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 4px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 4px;
}

.tool-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  padding: 0;
  min-width: 36px;
}

.tool-btn i {
  font-size: 14px;
}

.tool-btn .point-icon {
  font-size: 8px;
}

.action-btn {
  height: 32px;
  font-size: 10px;
  padding: 0 12px;
}

.style-section {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}





.results-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 16px 8px 16px;
}

.section-header h4 {
  margin: 0;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.results-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px 16px 16px;
}

.layer-item {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 8px;
  overflow: hidden;
}

.layer-header {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  background: #f8fafc;
}

.layer-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 10px;
}

.layer-info {
  flex: 1;
}

.layer-label {
  font-weight: 500;
  color: #1f2937;
  font-size: 11px;
}

.layer-count {
  font-size: 9px;
  color: #6b7280;
  margin-top: 1px;
}

.layer-actions {
  display: flex;
  gap: 2px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
  font-size: 0.75rem;
}

.empty-state i {
  font-size: 40px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state p {
  margin: 8px 0;
}

.empty-tip {
  font-size: 10px;
  opacity: 0.7;
}

/* 滚动条样式 */
.results-list::-webkit-scrollbar {
  width: 6px;
}

.results-list::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.results-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.results-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 属性表样式 */
.attribute-table-container {
  max-height: 500px;
  overflow: auto;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.table-header span {
  font-weight: 500;
  color: #374151;
  font-size: 0.75rem;
}

/* 表格内的输入框样式 */
.el-table .el-input {
  --el-input-border-color: transparent;
}

.el-table .el-input:hover {
  --el-input-border-color: #ddd;
}

.el-table .el-input.is-focus {
  --el-input-border-color: #409eff;
}
</style>
