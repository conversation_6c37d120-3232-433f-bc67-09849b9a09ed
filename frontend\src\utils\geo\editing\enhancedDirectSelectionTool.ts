import * as Cesium from "cesium"
import type { GeoFeature, FeatureType, GeoPoint } from '../core/types'
import { geoEventBus, GEO_EVENTS } from '../core/eventBus'

/**
 * 增强的直接选择工具 - 支持点击和框选
 */
export class DirectSelectionTool {
  private viewer: Cesium.Viewer
  private isActive = false
  private selectedFeatures: GeoFeature[] = []
  
  // 框选相关
  private isDrawing = false
  private startPosition: Cesium.Cartesian2 | null = null
  private selectionRectangle: HTMLElement | null = null

  constructor(viewer: Cesium.Viewer) {
    this.viewer = viewer
    this.createSelectionRectangle()
  }

  /**
   * 创建选择框元素
   */
  private createSelectionRectangle() {
    if (this.selectionRectangle) return

    this.selectionRectangle = document.createElement('div')
    this.selectionRectangle.style.cssText = `
      position: absolute;
      border: 2px dashed #00ff00;
      background-color: rgba(0, 255, 0, 0.1);
      display: none;
      pointer-events: none;
      z-index: 9999;
    `
    
    // 添加到viewer容器
    this.viewer.container.appendChild(this.selectionRectangle)
  }
  /**
   * 激活选择工具
   */
  activate() {
    console.log('🎯 激活增强选择工具')
    
    this.isActive = true
    
    // 禁用默认相机控制
    this.viewer.scene.screenSpaceCameraController.enableInputs = false
    
    // 设置鼠标样式
    this.viewer.canvas.style.cursor = 'crosshair'
    
    // 绑定事件处理器
    this.viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(
      this.onMouseDown.bind(this),
      Cesium.ScreenSpaceEventType.LEFT_DOWN
    )
    
    this.viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(
      this.onMouseMove.bind(this),
      Cesium.ScreenSpaceEventType.MOUSE_MOVE
    )
    
    this.viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(
      this.onMouseUp.bind(this),
      Cesium.ScreenSpaceEventType.LEFT_UP
    )
    
    // 添加一个备用的简单点击处理器
    this.viewer.cesiumWidget.screenSpaceEventHandler.setInputAction(
      this.onSimpleClick.bind(this),
      Cesium.ScreenSpaceEventType.LEFT_CLICK
    )
    
    console.log('✅ 增强选择工具已激活')
  }

  /**
   * 简单点击处理（备用方案）
   */
  private onSimpleClick(event: Cesium.ScreenSpaceEventHandler.PositionedEvent) {
    // 如果正在拖拽，忽略点击事件
    if (this.isDrawing) return
    
    console.log('🎯 简单点击选择，位置:', event.position)
    
    // 延迟100ms执行，确保拖拽检测完成
    setTimeout(() => {
      if (!this.isDrawing) {
        this.handleClickSelection(event.position)
      }
    }, 100)
  }

  /**
   * 鼠标按下事件
   */
  private onMouseDown(event: Cesium.ScreenSpaceEventHandler.PositionedEvent) {
    if (!this.isActive) return
    
    console.log('🖱️ 鼠标按下:', event.position)
    this.startPosition = event.position.clone()
    this.isDrawing = true
  }

  /**
   * 鼠标移动事件
   */
  private onMouseMove(event: Cesium.ScreenSpaceEventHandler.MotionEvent) {
    if (!this.isActive || !this.isDrawing || !this.startPosition) return
    
    this.showSelectionRectangle(this.startPosition, event.endPosition)
  }

  /**
   * 鼠标释放事件
   */
  private onMouseUp(event: Cesium.ScreenSpaceEventHandler.PositionedEvent) {
    if (!this.isActive) return
    
    console.log('🖱️ 鼠标释放:', event.position)
      if (this.isDrawing && this.startPosition) {
      const endPosition = event.position
      const distance = Cesium.Cartesian2.distance(this.startPosition, endPosition)
        console.log('🔍 鼠标移动距离分析:', {
        startPosition: { x: this.startPosition.x, y: this.startPosition.y },
        endPosition: { x: endPosition.x, y: endPosition.y },
        distance: distance,
        threshold: 20
      })
      
      if (distance < 20) { // 增加阈值到20像素，提高点击识别的容忍度
        // 点击选择
        console.log('🎯 识别为点击选择，距离:', distance)
        this.handleClickSelection(this.startPosition)
      } else {
        // 框选
        console.log('📦 识别为框选操作，距离:', distance)
        this.handleBoxSelection(this.startPosition, endPosition)
      }
    }
    
    // 重置状态
    this.isDrawing = false
    this.startPosition = null
    this.hideSelectionRectangle()
  }

  /**
   * 显示选择框
   */
  private showSelectionRectangle(start: Cesium.Cartesian2, end: Cesium.Cartesian2) {
    if (!this.selectionRectangle) return

    const minX = Math.min(start.x, end.x)
    const maxX = Math.max(start.x, end.x)
    const minY = Math.min(start.y, end.y)
    const maxY = Math.max(start.y, end.y)

    this.selectionRectangle.style.left = `${minX}px`
    this.selectionRectangle.style.top = `${minY}px`
    this.selectionRectangle.style.width = `${maxX - minX}px`
    this.selectionRectangle.style.height = `${maxY - minY}px`
    this.selectionRectangle.style.display = 'block'
  }

  /**
   * 隐藏选择框
   */
  private hideSelectionRectangle() {
    if (this.selectionRectangle) {
      this.selectionRectangle.style.display = 'none'
    }
  }  /**
   * 处理点击选择
   */
  private handleClickSelection(position: Cesium.Cartesian2) {
    console.log('🎯 处理点击选择，位置:', position)
    
    // 执行拾取
    const pickedObject = this.viewer.scene.pick(position)
    
    console.log('🎯 拾取结果:', pickedObject)
    console.log('🎯 拾取对象详细信息:', {
      hasPrimitive: !!pickedObject?.primitive,
      hasId: !!pickedObject?.id,
      idType: typeof pickedObject?.id,
      idValue: pickedObject?.id,
      primitive: pickedObject?.primitive
    })
    
    // 如果基本拾取失败，尝试使用drillPick获取所有对象
    if (!pickedObject || !pickedObject.id) {
      console.log('🔍 基本拾取失败，尝试 drillPick')
      const pickedObjects = this.viewer.scene.drillPick(position)
      console.log('🔍 drillPick 结果:', pickedObjects)
      
      if (pickedObjects && pickedObjects.length > 0) {
        for (const obj of pickedObjects) {
          if (obj.id) {
            console.log('🔍 从 drillPick 中找到实体:', obj.id)
            this.processPickedEntity(obj.id as Cesium.Entity)
            return
          }
        }
      }
    }
    
    if (Cesium.defined(pickedObject) && pickedObject.id) {
      const entity = pickedObject.id as Cesium.Entity
      this.processPickedEntity(entity)
    } else {
      console.log('❌ 未选中任何实体')
      this.clearSelection()
    }
  }

  /**
   * 处理选中的实体
   */
  private processPickedEntity(entity: Cesium.Entity) {    console.log('✅ 选中实体:', entity.id, entity.name)
    console.log('🔍 实体详细信息:', {
      id: entity.id,
      name: entity.name,
      hasPoint: !!entity.point,
      hasPosition: !!entity.position,
      hasPolyline: !!entity.polyline,
      hasPolygon: !!entity.polygon,
      position: entity.position,
      positionType: entity.position?.constructor?.name
    })
    
    // 转换为 GeoFeature
    const feature = this.convertEntityToGeoFeature(entity)
    if (feature) {
      console.log('✅ 成功转换为 GeoFeature:', feature)
      this.selectFeature(feature)
    } else {
      console.warn('❌ 转换 GeoFeature 失败')
    }
  }
  /**
   * 处理框选
   */
  private handleBoxSelection(start: Cesium.Cartesian2, end: Cesium.Cartesian2) {
    console.log('📦 处理框选:', start, end)
    
    const minX = Math.min(start.x, end.x)
    const maxX = Math.max(start.x, end.x)
    const minY = Math.min(start.y, end.y)
    const maxY = Math.max(start.y, end.y)
    
    console.log('📦 框选区域:', { minX, minY, maxX, maxY, width: maxX - minX, height: maxY - minY })
    
    // 如果框选区域太小，转为点击选择
    const boxWidth = maxX - minX
    const boxHeight = maxY - minY
    
    if (boxWidth < 5 && boxHeight < 5) {
      console.log('📦 框选区域太小，转为点击选择')
      this.handleClickSelection(start)
      return
    }
    
    // 使用更全面的方法收集所有可能的实体
    const allEntities: Cesium.Entity[] = []
    
    // 1. 检查 viewer.entities
    allEntities.push(...this.viewer.entities.values)
    
    // 2. 检查所有数据源中的实体
    for (let i = 0; i < this.viewer.dataSources.length; i++) {
      const dataSource = this.viewer.dataSources.get(i)
      if (dataSource && dataSource.entities) {
        allEntities.push(...dataSource.entities.values)
      }
    }
    
    console.log('📦 总计要检查的实体数量:', allEntities.length)
    
    // 遍历所有实体，检查是否在框选区域内
    const selectedEntities: Cesium.Entity[] = []
    
    allEntities.forEach((entity) => {
      if (this.isEntityInBox(entity, minX, minY, maxX, maxY)) {
        selectedEntities.push(entity)
      }
    })
    
    console.log('📦 框选到的实体数量:', selectedEntities.length)
    
    // 清除之前的选择
    this.clearSelection()
    
    // 选择框选到的实体
    selectedEntities.forEach(entity => {
      const feature = this.convertEntityToGeoFeature(entity)
      if (feature) {
        this.selectedFeatures.push(feature)
        this.createHighlight(feature)
      }
    })
      if (this.selectedFeatures.length > 0) {
      // 触发选择事件 - 传递不含 Cesium 实体的副本
      const serializableFeatures = this.selectedFeatures.map(feature => ({
        id: feature.id,
        type: feature.type,
        points: feature.points,
        properties: feature.properties,
        timestamp: feature.timestamp
        // 不包含 entity，避免序列化问题
      }))
      geoEventBus.emit(GEO_EVENTS.SELECTION_CHANGED, serializableFeatures)
      console.log('🎉 框选完成，选择了', this.selectedFeatures.length, '个要素')
    } else {
      console.log('📦 框选完成，但没有选择到任何要素')
      // 确保触发空选择事件
      geoEventBus.emit(GEO_EVENTS.SELECTION_CHANGED, [])
    }
  }/**
   * 检查实体是否在框选区域内
   */
  private isEntityInBox(entity: Cesium.Entity, minX: number, minY: number, maxX: number, maxY: number): boolean {
    try {
      // 获取实体的所有关键点
      const positions: Cesium.Cartesian3[] = []
      
      // 只为前几个实体输出详细调试信息，避免控制台信息过多
      const shouldDebug = Math.random() < 0.1 // 10%的概率输出调试信息
      
      if (shouldDebug) {
        console.log('🔍 检查实体:', entity.id, '是否在框选区域内')
      }
        if (entity.position) {        // 点要素 - 统一使用point方式（已优化layerManager确保不使用billboard）
        let pos: Cesium.Cartesian3 | undefined
        
        if (shouldDebug) {
          console.log('🔍 处理 entity.position:', entity.position, '实体类型:', {
            hasPoint: !!entity.point,
            pointVisible: entity.point?.show?.getValue?.(Cesium.JulianDate.now()) ?? entity.point?.show ?? true
          })
        }
        
        if (entity.position instanceof Cesium.ConstantPositionProperty) {
          pos = entity.position.getValue(Cesium.JulianDate.now())
        } else if (typeof entity.position.getValue === 'function') {
          pos = entity.position.getValue(Cesium.JulianDate.now())
        } else {
          pos = entity.position as any
        }
        
        if (pos) {
          positions.push(pos)
          if (shouldDebug) {
            console.log('🔍 点要素位置:', pos, '(统一point方式)')
          }
        }
      }
      
      if (entity.polyline?.positions) {
        // 线要素
        let linePositions: Cesium.Cartesian3[] | undefined
        
        if (entity.polyline.positions instanceof Cesium.ConstantProperty) {
          linePositions = entity.polyline.positions.getValue(Cesium.JulianDate.now())
        } else if (typeof entity.polyline.positions.getValue === 'function') {
          linePositions = entity.polyline.positions.getValue(Cesium.JulianDate.now())
        } else {
          linePositions = entity.polyline.positions as any
        }
        
        if (linePositions && Array.isArray(linePositions)) {
          positions.push(...linePositions)
        }
      }
      
      if (entity.polygon?.hierarchy) {
        // 面要素处理...
        let hierarchy: any
        
        if (entity.polygon.hierarchy instanceof Cesium.ConstantProperty) {
          hierarchy = entity.polygon.hierarchy.getValue(Cesium.JulianDate.now())
        } else if (typeof entity.polygon.hierarchy.getValue === 'function') {
          hierarchy = entity.polygon.hierarchy.getValue(Cesium.JulianDate.now())
        } else {
          hierarchy = entity.polygon.hierarchy as any
        }
        
        if (hierarchy) {
          let polygonPositions: Cesium.Cartesian3[] = []
          
          if (Array.isArray(hierarchy)) {
            polygonPositions = hierarchy
          } else if (hierarchy.positions) {
            polygonPositions = hierarchy.positions
          } else if (hierarchy.outerRing) {
            polygonPositions = hierarchy.outerRing
          }
          
          if (polygonPositions && polygonPositions.length > 0) {
            positions.push(...polygonPositions)
          }
        }
      }
      
      if (positions.length === 0) {
        return false
      }
        // 检查是否有任何点在框选区域内
      for (let i = 0; i < positions.length; i++) {
        const position = positions[i]
        const screenPosition = Cesium.SceneTransforms.worldToWindowCoordinates(this.viewer.scene, position)
        
        if (screenPosition) {
          // 增加一些容错范围，扩大检测区域
          const tolerance = 5 // 5像素的容错范围
          const inBox = screenPosition.x >= (minX - tolerance) && screenPosition.x <= (maxX + tolerance) &&
                       screenPosition.y >= (minY - tolerance) && screenPosition.y <= (maxY + tolerance)
          
          if (shouldDebug) {
            console.log('🔍 位置', i, '屏幕坐标:', { 
              x: Math.round(screenPosition.x), 
              y: Math.round(screenPosition.y) 
            }, '框选区域:', { 
              minX: Math.round(minX), 
              minY: Math.round(minY), 
              maxX: Math.round(maxX), 
              maxY: Math.round(maxY) 
            }, '在框内:', inBox)
          }
          
          if (inBox) {
            if (shouldDebug) {
              console.log('✅ 实体', entity.id, '在框选区域内')
            }
            return true
          }
        }
      }
      
      return false
    } catch (error) {
      console.warn('检查实体是否在框选区域内时出错:', error)
      return false
    }
  }
  /**
   * 停用选择工具
   */
  deactivate(clearSelection = true) {
    console.log('🎯 停用增强选择工具, clearSelection:', clearSelection)
    console.log('🎯 停用前，当前选择数量:', this.selectedFeatures.length)
    
    this.isActive = false
    
    // 恢复默认事件处理    this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOWN)
    this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE)
    this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP)
    this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK)
    
    // 恢复鼠标样式
    this.viewer.canvas.style.cursor = 'default'
    
    // 恢复相机控制
    this.viewer.scene.screenSpaceCameraController.enableInputs = true
    
    // 隐藏选择框
    this.hideSelectionRectangle()
    
    // 根据参数决定是否清除选择
    if (clearSelection) {
      console.log('🎯 清除选择中...')
      this.clearSelection()
    } else {
      console.log('🎯 保留选择状态，当前选择数量:', this.selectedFeatures.length)
    }
    
    console.log('✅ 增强选择工具已停用，最终选择数量:', this.selectedFeatures.length)
  }  /**
   * 转换Entity为GeoFeature
   */
  private convertEntityToGeoFeature(entity: Cesium.Entity): GeoFeature | null {
    try {
      console.log('🔄 开始转换 Entity 为 GeoFeature:', entity.id)
      
      let type: FeatureType = 'point'
      
      if (entity.polygon) {
        type = 'polygon'
        console.log('🔍 识别为多边形要素')
      } else if (entity.polyline) {
        type = 'line'
        console.log('🔍 识别为线要素')
      } else if (entity.point || entity.billboard || entity.position) {
        type = 'point'
        console.log('🔍 识别为点要素 (类型检测):', {
          hasPoint: !!entity.point,
          hasBillboard: !!entity.billboard,
          hasPosition: !!entity.position,
          pointVisible: entity.point?.show?.getValue?.(Cesium.JulianDate.now()) ?? entity.point?.show,
          billboardVisible: entity.billboard?.show?.getValue?.(Cesium.JulianDate.now()) ?? entity.billboard?.show
        })
      }
      
      console.log('🔍 要素类型:', type)
      
      const points = this.extractPoints(entity)
      console.log('🔍 提取的点数量:', points.length)
      
      if (points.length === 0) {
        console.warn('⚠️ 无法提取有效的坐标点')
        return null
      }
      
      const feature: GeoFeature = {
        id: entity.id || `feature-${Date.now()}`,
        type: type,
        points: points,
        properties: {
          name: entity.name || entity.id || '未命名要素',
          fillColor: '#0066cc',
          strokeColor: '#003d7a',
          strokeWidth: 2
        },
        timestamp: new Date(),
        entity: entity // 保存原始entity引用
      }
      
      console.log('✅ 转换完成:', feature)
      return feature
    } catch (error) {
      console.error('❌ 转换Entity为GeoFeature时出错:', error)
      return null
    }
  }/**
   * 从实体中提取点
   */
  private extractPoints(entity: Cesium.Entity): GeoPoint[] {
    const points: GeoPoint[] = []
      try {
      console.log('🔍 开始提取坐标点，实体信息:', {
        hasPosition: !!entity.position,
        hasPolyline: !!entity.polyline,
        hasPolygon: !!entity.polygon,
        hasPoint: !!entity.point,
        hasBillboard: !!entity.billboard,
        positionType: entity.position?.constructor?.name,
        pointVisible: entity.point?.show?.getValue?.(Cesium.JulianDate.now()) ?? entity.point?.show ?? 'unknown',
        billboardVisible: entity.billboard?.show?.getValue?.(Cesium.JulianDate.now()) ?? entity.billboard?.show ?? 'unknown'
      })
      
      if (entity.position) {
        // 点要素 - 支持 point 和 billboard 两种显示方式
        let position: Cesium.Cartesian3 | undefined
        
        console.log('🔍 处理 entity.position:', entity.position)
        
        if (entity.position instanceof Cesium.ConstantPositionProperty) {
          position = entity.position.getValue(Cesium.JulianDate.now())
          console.log('🔍 ConstantPositionProperty 方式获取位置:', position)
        } else if (typeof entity.position.getValue === 'function') {
          position = entity.position.getValue(Cesium.JulianDate.now())
          console.log('🔍 getValue 方法获取位置:', position)
        } else if (entity.position instanceof Cesium.Cartesian3) {
          position = entity.position
          console.log('🔍 直接 Cartesian3 位置:', position)
        } else {
          // 尝试直接作为值使用
          position = entity.position as any
          console.log('🔍 直接使用 position:', position)
        }
        
        console.log('🔍 点要素坐标提取 (point/billboard):', { 
          originalPosition: entity.position, 
          extractedPosition: position,
          isCartesian3: position instanceof Cesium.Cartesian3,
          entityType: entity.point ? 'point' : entity.billboard ? 'billboard' : 'position-only'
        })
        
        if (position && position instanceof Cesium.Cartesian3) {
          const cartographic = Cesium.Cartographic.fromCartesian(position)
          const geoPoint = {
            longitude: Cesium.Math.toDegrees(cartographic.longitude),
            latitude: Cesium.Math.toDegrees(cartographic.latitude),
            height: cartographic.height || 0
          }
          points.push(geoPoint)
          console.log('✅ 成功提取坐标 (支持billboard):', geoPoint)
        } else {
          console.warn('⚠️ 无法提取要素的有效位置, position:', position, 'type:', typeof position)
        }
      } else if (entity.polyline?.positions) {
        // 线要素
        console.log('🔍 处理线要素坐标')
        const positions = entity.polyline.positions.getValue ? 
          entity.polyline.positions.getValue(Cesium.JulianDate.now()) : 
          entity.polyline.positions
          if (positions && Array.isArray(positions)) {
          positions.forEach((position: Cesium.Cartesian3) => {
            const cartographic = Cesium.Cartographic.fromCartesian(position)
            points.push({
              longitude: Cesium.Math.toDegrees(cartographic.longitude),
              latitude: Cesium.Math.toDegrees(cartographic.latitude),
              height: cartographic.height || 0
            })
          })
          console.log('✅ 成功提取线要素坐标，点数:', points.length)
        }
      } else if (entity.polygon?.hierarchy) {
        // 面要素
        console.log('🔍 处理面要素坐标')
        const hierarchy = entity.polygon.hierarchy.getValue ? 
          entity.polygon.hierarchy.getValue(Cesium.JulianDate.now()) : 
          entity.polygon.hierarchy
        
        if (hierarchy) {
          let positions: Cesium.Cartesian3[] = []
          
          if (Array.isArray(hierarchy)) {
            positions = hierarchy
          } else if (hierarchy.positions) {
            positions = hierarchy.positions
          } else if (hierarchy.outerRing) {
            positions = hierarchy.outerRing
          }
            positions.forEach((position: Cesium.Cartesian3) => {
            const cartographic = Cesium.Cartographic.fromCartesian(position)
            points.push({
              longitude: Cesium.Math.toDegrees(cartographic.longitude),
              latitude: Cesium.Math.toDegrees(cartographic.latitude),
              height: cartographic.height || 0
            })
          })        }
      } else {
        console.warn('⚠️ 实体没有可识别的几何数据:', {
          hasPosition: !!entity.position,
          hasPolyline: !!entity.polyline,
          hasPolygon: !!entity.polygon,
          hasPoint: !!entity.point,
          hasBillboard: !!entity.billboard
        })
      }
    } catch (error) {
      console.error('❌ 提取点坐标时出错:', error)
    }
    
    console.log('🔍 坐标提取完成，总点数:', points.length)
    return points
  }

  /**
   * 选择要素
   */
  private selectFeature(feature: GeoFeature) {
    // 添加到选择列表
    this.selectedFeatures.push(feature)
    
    // 创建高亮
    this.createHighlight(feature)
      // 触发选择事件 - 传递可序列化的要素副本
    const serializableFeature = {
      id: feature.id,
      type: feature.type,
      points: feature.points,
      properties: feature.properties,
      timestamp: feature.timestamp
      // 不包含 entity，避免序列化问题
    }
    geoEventBus.emit(GEO_EVENTS.SELECTION_CHANGED, [serializableFeature])
    
    console.log('✅ 要素已选择:', feature.id)
  }  /**
   * 创建高亮显示
   */
  private createHighlight(_feature: GeoFeature) {
    if (!_feature.entity) return
    
    const entity = _feature.entity as any // 使用any来避免类型检查问题
    
    // 存储原始样式（如果还没有存储）
    if (!entity._originalStyle) {
      entity._originalStyle = {}
      
      if (entity.polygon) {
        // 保存原始样式
        entity._originalStyle.polygon = {
          material: entity.polygon.material,
          outline: entity.polygon.outline,
          outlineColor: entity.polygon.outlineColor,
          outlineWidth: entity.polygon.outlineWidth
        }
        
        // 应用高亮样式 - 使用ConstantProperty包装
        try {
          entity.polygon.material = new Cesium.ColorMaterialProperty(Cesium.Color.YELLOW.withAlpha(0.5))
          entity.polygon.outline = new Cesium.ConstantProperty(true)
          entity.polygon.outlineColor = new Cesium.ConstantProperty(Cesium.Color.YELLOW)
          entity.polygon.outlineWidth = new Cesium.ConstantProperty(3)
        } catch (error) {
          console.warn('设置面高亮样式时出错:', error)
        }
      }
      
      if (entity.polyline) {
        // 保存原始样式
        entity._originalStyle.polyline = {
          material: entity.polyline.material,
          width: entity.polyline.width
        }
        
        // 应用高亮样式
        try {
          entity.polyline.material = new Cesium.ColorMaterialProperty(Cesium.Color.YELLOW)
          entity.polyline.width = new Cesium.ConstantProperty(5)
        } catch (error) {
          console.warn('设置线高亮样式时出错:', error)
        }
      }
        if (entity.point) {
        // 保存原始样式
        entity._originalStyle.point = {
          color: entity.point.color,
          outlineColor: entity.point.outlineColor,
          outlineWidth: entity.point.outlineWidth,
          pixelSize: entity.point.pixelSize
        }
        
        // 应用高亮样式
        try {
          entity.point.color = new Cesium.ConstantProperty(Cesium.Color.YELLOW)
          entity.point.outlineColor = new Cesium.ConstantProperty(Cesium.Color.BLACK)
          entity.point.outlineWidth = new Cesium.ConstantProperty(3)
          entity.point.pixelSize = new Cesium.ConstantProperty(15)
        } catch (error) {
          console.warn('设置点高亮样式时出错:', error)
        }
      }      if (entity.billboard) {
        // 保存原始样式
        entity._originalStyle.billboard = {
          color: entity.billboard.color,
          scale: entity.billboard.scale,
          image: entity.billboard.image,
          pixelOffset: entity.billboard.pixelOffset
        }
          // 应用高亮样式
        try {
          // 方案1：改变颜色（如果支持）
          if (entity.billboard.color) {
            entity.billboard.color = new Cesium.ConstantProperty(Cesium.Color.YELLOW)
            console.log('📍 Billboard颜色高亮已应用:', entity.id)
          }
            // 方案2：放大billboard（总是应用）
          let currentScale = 1
          if (entity.billboard.scale) {
            currentScale = entity.billboard.scale.getValue ? entity.billboard.scale.getValue() : (entity.billboard.scale || 1)
          }
          entity.billboard.scale = new Cesium.ConstantProperty(currentScale * 1.5)
          
          // 方案3：添加跳动效果（可选）
          if (entity.billboard.pixelOffset) {
            // 轻微的位置偏移来创建"跳动"效果
            const originalOffset = entity.billboard.pixelOffset.getValue ? entity.billboard.pixelOffset.getValue() : entity.billboard.pixelOffset
            entity.billboard.pixelOffset = new Cesium.ConstantProperty(new Cesium.Cartesian2(originalOffset.x, originalOffset.y - 3))
          } else {
            entity.billboard.pixelOffset = new Cesium.ConstantProperty(new Cesium.Cartesian2(0, -3))
          }
          
          // 方案4：如果以上方案效果不明显，创建临时高亮点
          const needsExtraHighlight = !entity.billboard.color || 
                                    entity.billboard.color.getValue(Cesium.JulianDate.now()).equals(Cesium.Color.WHITE) ||
                                    entity.billboard.color.getValue(Cesium.JulianDate.now()).alpha < 0.5
          
          if (needsExtraHighlight) {
            const position = entity.position
            if (position) {
              entity._highlightPoint = this.viewer.entities.add({
                position: position,
                point: {
                  pixelSize: 25,
                  color: Cesium.Color.YELLOW.withAlpha(0.8),
                  outlineColor: Cesium.Color.RED,
                  outlineWidth: 3,
                  heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                  scaleByDistance: new Cesium.NearFarScalar(1.5e2, 1.0, 1.5e7, 0.5)
                }
              })
              console.log('📍 为Billboard创建了增强高亮点:', entity.id)
            }
          }
          
          console.log('📍 Billboard高亮样式已应用:', entity.id, {
            colorChanged: !!entity.billboard.color,
            scaleChanged: true,
            hasExtraHighlight: !!entity._highlightPoint,
            newScale: currentScale * 1.5
          })
        } catch (error) {
          console.warn('设置billboard高亮样式时出错:', error)
        }
      }
      
      console.log('✨ 已为实体添加高亮:', entity.id)
    }
  }
  /**
   * 清除选择
   */
  private clearSelection() {
    console.log('🧹 清除选择')
    
    // 清除高亮
    this.selectedFeatures.forEach(feature => {
      this.clearHighlight(feature)
    })
    
    this.selectedFeatures = []
    
    // 触发清空选择事件
    geoEventBus.emit(GEO_EVENTS.SELECTION_CHANGED, [])
  }

  /**
   * 清除要素高亮
   */
  private clearHighlight(feature: GeoFeature) {
    if (!feature.entity) return
    
    const entity = feature.entity as any
    
    if (entity._originalStyle) {
      try {
        // 恢复面样式
        if (entity._originalStyle.polygon && entity.polygon) {
          entity.polygon.material = entity._originalStyle.polygon.material
          entity.polygon.outline = entity._originalStyle.polygon.outline
          entity.polygon.outlineColor = entity._originalStyle.polygon.outlineColor
          entity.polygon.outlineWidth = entity._originalStyle.polygon.outlineWidth
        }
        
        // 恢复线样式
        if (entity._originalStyle.polyline && entity.polyline) {
          entity.polyline.material = entity._originalStyle.polyline.material
          entity.polyline.width = entity._originalStyle.polyline.width
        }
          // 恢复点样式
        if (entity._originalStyle.point && entity.point) {
          entity.point.color = entity._originalStyle.point.color
          entity.point.outlineColor = entity._originalStyle.point.outlineColor
          entity.point.outlineWidth = entity._originalStyle.point.outlineWidth
          entity.point.pixelSize = entity._originalStyle.point.pixelSize
        }        // 恢复billboard样式
        if (entity._originalStyle.billboard && entity.billboard) {
          entity.billboard.color = entity._originalStyle.billboard.color
          entity.billboard.scale = entity._originalStyle.billboard.scale
          entity.billboard.pixelOffset = entity._originalStyle.billboard.pixelOffset
          
          // 移除临时高亮点
          if (entity._highlightPoint) {
            this.viewer.entities.remove(entity._highlightPoint)
            delete entity._highlightPoint
            console.log('📍 已移除Billboard的临时高亮点:', entity.id)
          }
          
          console.log('📍 Billboard高亮样式已恢复:', entity.id)
        }
        
        // 清除保存的原始样式
        delete entity._originalStyle
        
        console.log('🧹 已清除实体高亮:', entity.id)
      } catch (error) {
        console.warn('清除高亮时出错:', error)
      }
    }
  }

  /**
   * 获取当前选择的要素
   */
  getSelectedFeatures(): GeoFeature[] {
    return [...this.selectedFeatures]
  }

  /**
   * 清除选择（公共方法）
   */
  public clearSelectionPublic() {
    this.clearSelection()
  }

  /**
   * 销毁选择工具
   */
  destroy() {
    this.deactivate()
    
    // 移除选择框元素
    if (this.selectionRectangle && this.selectionRectangle.parentNode) {
      this.selectionRectangle.parentNode.removeChild(this.selectionRectangle)
      this.selectionRectangle = null
    }
  }
}
