"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const bcrypt = require("bcryptjs");
const user_entity_1 = require("../entities/user.entity");
const user_session_entity_1 = require("../entities/user-session.entity");
const audit_log_entity_1 = require("../entities/audit-log.entity");
const role_entity_1 = require("../entities/role.entity");
const mail_service_1 = require("../mail/mail.service");
let AuthService = AuthService_1 = class AuthService {
    userRepository;
    sessionRepository;
    auditRepository;
    roleRepository;
    jwtService;
    mailService;
    logger = new common_1.Logger(AuthService_1.name);
    constructor(userRepository, sessionRepository, auditRepository, roleRepository, jwtService, mailService) {
        this.userRepository = userRepository;
        this.sessionRepository = sessionRepository;
        this.auditRepository = auditRepository;
        this.roleRepository = roleRepository;
        this.jwtService = jwtService;
        this.mailService = mailService;
    }
    async validateUser(username, password) {
        try {
            this.logger.log(`Attempting to validate user: ${username}`);
            const user = await this.userRepository.findOne({
                where: { username },
                relations: ['roles'],
            });
            if (!user) {
                this.logger.warn(`User not found: ${username}`);
                await this.logSecurityEvent('LOGIN_ATTEMPT_INVALID_USERNAME', undefined, username);
                return null;
            }
            this.logger.log(`User found: ${user.username}, active: ${user.isActive}, locked: ${user.isLocked}`);
            if (user.isLocked) {
                await this.logSecurityEvent('LOGIN_ATTEMPT_LOCKED_ACCOUNT', user.id, username);
                return null;
            }
            if (!user.isActive) {
                await this.logSecurityEvent('LOGIN_ATTEMPT_INACTIVE_ACCOUNT', user.id, username);
                return null;
            }
            this.logger.log(`Validating password for user: ${user.username}`);
            this.logger.log(`Stored password hash: ${user.passwordHash.substring(0, 20)}...`);
            this.logger.log(`Input password: ${password}`);
            const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
            this.logger.log(`Password validation result: ${isPasswordValid}`);
            if (!isPasswordValid) {
                await this.handleFailedLogin(user);
                await this.logSecurityEvent('LOGIN_ATTEMPT_INVALID_PASSWORD', user.id, username);
                return null;
            }
            if (user.failedLoginAttempts > 0) {
                await this.userRepository.update(user.id, {
                    login_attempts: 0,
                    locked_until: null,
                });
            }
            await this.logSecurityEvent('LOGIN_SUCCESS', user.id, username);
            return user;
        }
        catch (error) {
            this.logger.error(`Error validating user: ${error.message}`, error.stack);
            return null;
        }
    }
    async login(loginDto, userAgent, ipAddress) {
        const { username, password } = loginDto;
        const user = await this.validateUser(username, password);
        if (!user) {
            throw new common_1.UnauthorizedException('用户名或密码错误');
        }
        const tokens = await this.generateTokens(user);
        await this.createUserSession(user, tokens.refreshToken, userAgent, ipAddress);
        await this.userRepository.update(user.id, {
            last_login: new Date(),
        });
        const permissions = this.getUserPermissions(user);
        return {
            accessToken: tokens.accessToken,
            refreshToken: tokens.refreshToken,
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                fullName: user.fullName,
                roles: user.roles.map(role => role.name),
                permissions,
                isActive: user.isActive,
            },
            expiresIn: 3600,
        };
    }
    async refreshToken(refreshToken) {
        try {
            const payload = this.jwtService.verify(refreshToken, {
                secret: process.env.JWT_REFRESH_SECRET,
            });
            const session = await this.sessionRepository.findOne({
                where: {
                    refresh_token: refreshToken,
                },
                relations: ['user', 'user.roles'],
            });
            if (!session || session.expiresAt < new Date()) {
                throw new common_1.UnauthorizedException('刷新令牌无效或已过期');
            }
            if (!session.user.isActive) {
                throw new common_1.UnauthorizedException('用户账户已被禁用');
            }
            const accessToken = await this.generateAccessToken(session.user);
            await this.logSecurityEvent('TOKEN_REFRESH', session.user.id);
            return {
                accessToken,
                expiresIn: 3600,
            };
        }
        catch (error) {
            this.logger.error(`Error refreshing token: ${error.message}`, error.stack);
            throw new common_1.UnauthorizedException('刷新令牌无效');
        }
    }
    async logout(refreshToken) {
        try {
            const session = await this.sessionRepository.findOne({
                where: { refresh_token: refreshToken },
                relations: ['user'],
            });
            if (session) {
                await this.sessionRepository.update(session.id, {
                    expires_at: new Date(),
                });
                await this.logSecurityEvent('LOGOUT', session.user.id);
            }
        }
        catch (error) {
            this.logger.error(`Error during logout: ${error.message}`, error.stack);
        }
    }
    async logoutAllDevices(userId) {
        try {
            await this.sessionRepository.update({ user_id: userId }, { expires_at: new Date() });
            await this.logSecurityEvent('LOGOUT_ALL_DEVICES', userId);
        }
        catch (error) {
            this.logger.error(`Error logging out all devices: ${error.message}`, error.stack);
            throw error;
        }
    }
    async changePassword(userId, changePasswordDto) {
        const { currentPassword, newPassword } = changePasswordDto;
        const user = await this.userRepository.findOne({
            where: { user_id: userId },
        });
        if (!user) {
            throw new common_1.BadRequestException('用户不存在');
        }
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash);
        if (!isCurrentPasswordValid) {
            await this.logSecurityEvent('PASSWORD_CHANGE_FAILED_WRONG_CURRENT', userId);
            throw new common_1.BadRequestException('当前密码错误');
        }
        const isSamePassword = await bcrypt.compare(newPassword, user.passwordHash);
        if (isSamePassword) {
            throw new common_1.BadRequestException('新密码不能与当前密码相同');
        }
        const saltRounds = 12;
        const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);
        await this.userRepository.update(userId, {
            password_hash: newPasswordHash,
            password_changed_at: new Date(),
        });
        await this.logSecurityEvent('PASSWORD_CHANGED', userId);
        await this.logoutAllDevices(userId);
        this.logger.log(`Password changed for user: ${user.username}`);
    }
    async resetPassword(username, newPassword) {
        try {
            const user = await this.userRepository.findOne({
                where: { username },
            });
            if (!user) {
                throw new common_1.BadRequestException('用户不存在');
            }
            const saltRounds = 12;
            const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);
            await this.userRepository.update(user.user_id, {
                password_hash: newPasswordHash,
                password_changed_at: new Date(),
                login_attempts: 0,
                locked_until: null,
            });
            await this.logSecurityEvent('PASSWORD_RESET_BY_ADMIN', user.user_id, `Password reset by admin for user: ${username}`);
            await this.logoutAllDevices(user.user_id);
            this.logger.log(`Password reset successfully for user: ${username}`);
            return {
                message: `用户 ${username} 的密码已重置为: ${newPassword}`
            };
        }
        catch (error) {
            this.logger.error(`Error resetting password for user ${username}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async validateJwtPayload(payload) {
        const user = await this.userRepository.findOne({
            where: { user_id: payload.sub },
            relations: ['roles'],
        });
        if (!user || !user.isActive) {
            throw new common_1.UnauthorizedException('用户不存在或已被禁用');
        }
        return user;
    }
    async generateTokens(user) {
        const payload = {
            username: user.username,
            sub: user.id,
            roles: user.roles.map(role => role.name),
            permissions: this.getUserPermissions(user),
        };
        const [accessToken, refreshToken] = await Promise.all([
            this.jwtService.signAsync(payload, {
                secret: process.env.JWT_SECRET,
                expiresIn: '1h',
            }),
            this.jwtService.signAsync({ sub: user.id, type: 'refresh' }, {
                secret: process.env.JWT_REFRESH_SECRET,
                expiresIn: '7d',
            }),
        ]);
        return { accessToken, refreshToken };
    }
    async generateAccessToken(user) {
        const payload = {
            username: user.username,
            sub: user.id,
            roles: user.roles.map(role => role.name),
            permissions: this.getUserPermissions(user),
        };
        return this.jwtService.signAsync(payload, {
            secret: process.env.JWT_SECRET,
            expiresIn: '1h',
        });
    }
    async createUserSession(user, refreshToken, userAgent, ipAddress) {
        const session = this.sessionRepository.create({
            user_id: user.id,
            refresh_token: refreshToken,
            user_agent: userAgent,
            ip_address: ipAddress,
            created_at: new Date(),
            expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        });
        return this.sessionRepository.save(session);
    }
    async handleFailedLogin(user) {
        const newFailedAttempts = user.failedLoginAttempts + 1;
        const maxAttempts = 5;
        const lockDurationMinutes = 15;
        const updateData = {
            login_attempts: newFailedAttempts,
        };
        if (newFailedAttempts >= maxAttempts) {
            updateData.locked_until = new Date(Date.now() + lockDurationMinutes * 60 * 1000);
        }
        await this.userRepository.update(user.id, updateData);
    }
    getUserPermissions(user) {
        const permissions = new Set();
        if (user.roles && Array.isArray(user.roles)) {
            user.roles.forEach(role => {
                const rolePermissions = role.getPermissions();
                rolePermissions.forEach(permission => {
                    permissions.add(permission);
                });
            });
        }
        return Array.from(permissions);
    }
    async logSecurityEvent(action, userId, details) {
        try {
            const auditLog = this.auditRepository.create({
                user_id: userId,
                action,
                resource: 'AUTH',
                details,
                created_at: new Date(),
            });
            await this.auditRepository.save(auditLog);
        }
        catch (error) {
            this.logger.error(`Error logging security event: ${error.message}`, error.stack);
        }
    }
    async register(registerDto) {
        try {
            const { username, email, emailCode, password, confirmPassword, fullName, phone } = registerDto;
            if (password !== confirmPassword) {
                throw new common_1.BadRequestException('密码和确认密码不一致');
            }
            const isCodeValid = await this.mailService.verifyCode(email, emailCode, 'register');
            if (!isCodeValid) {
                throw new common_1.BadRequestException('邮箱验证码无效或已过期');
            }
            const existingUserByUsername = await this.userRepository.findOne({
                where: { username },
            });
            if (existingUserByUsername) {
                throw new common_1.BadRequestException('用户名已被使用');
            }
            const existingUserByEmail = await this.userRepository.findOne({
                where: { email },
            });
            if (existingUserByEmail) {
                throw new common_1.BadRequestException('邮箱已被使用');
            }
            const viewerRole = await this.roleRepository.findOne({
                where: { name: 'user' },
            });
            if (!viewerRole) {
                throw new common_1.BadRequestException('默认用户角色不存在，请联系管理员');
            }
            const user = new user_entity_1.User();
            user.username = username;
            user.email = email;
            user.password = password;
            user.full_name = fullName || '';
            user.phone = phone || '';
            user.is_active = true;
            user.is_first_login = true;
            user.roles = [viewerRole];
            user.created_at = new Date();
            const savedUser = await this.userRepository.save(user);
            await this.logSecurityEvent('USER_REGISTER', savedUser.id, `User registered: ${username}`);
            this.logger.log(`User registered successfully: ${username}`);
            return {
                message: '用户注册成功',
                user: {
                    id: savedUser.id,
                    username: savedUser.username,
                    email: savedUser.email,
                    fullName: savedUser.fullName,
                },
            };
        }
        catch (error) {
            this.logger.error(`Error during user registration: ${error.message}`, error.stack);
            throw error;
        }
    }
    async sendEmailCode(email, type = 'login') {
        try {
            this.logger.log(`Sending email verification code to: ${email}`);
            const user = await this.userRepository.findOne({
                where: { email },
            });
            if (type === 'login') {
                if (!user) {
                    throw new common_1.BadRequestException('该邮箱地址未注册');
                }
                if (!user.isActive) {
                    throw new common_1.BadRequestException('账户已被禁用');
                }
                if (user.isLocked) {
                    throw new common_1.BadRequestException('账户已被锁定');
                }
            }
            if (type === 'register') {
                if (user) {
                    throw new common_1.BadRequestException('该邮箱地址已被注册');
                }
            }
            if (type === 'reset') {
                if (!user) {
                    throw new common_1.BadRequestException('该邮箱地址未注册');
                }
                if (!user.isActive) {
                    throw new common_1.BadRequestException('账户已被禁用');
                }
            }
            try {
                await this.mailService.sendVerificationCode(email, type);
                this.logger.log(`Email verification code sent successfully to: ${email}`);
            }
            catch (mailError) {
                this.logger.error(`Mail sending failed, but verification code generated: ${mailError.message}`);
            }
            return {
                message: '验证码已发送到您的邮箱，请查收'
            };
        }
        catch (error) {
            this.logger.error(`Error sending email verification code: ${error.message}`, error.stack);
            throw error;
        }
    }
    async verifyEmailCode(email, code) {
        try {
            return await this.mailService.verifyCode(email, code);
        }
        catch (error) {
            this.logger.error(`Error verifying email code: ${error.message}`, error.stack);
            throw new common_1.BadRequestException('验证码验证失败');
        }
    }
    async loginWithEmail(emailLoginDto) {
        try {
            const { email, code } = emailLoginDto;
            this.logger.log(`Attempting email login for: ${email}`);
            const isCodeValid = await this.verifyEmailCode(email, code);
            if (!isCodeValid) {
                await this.logSecurityEvent('LOGIN_ATTEMPT_INVALID_EMAIL_CODE', undefined, email);
                throw new common_1.UnauthorizedException('验证码无效或已过期');
            }
            const user = await this.userRepository.findOne({
                where: { email },
                relations: ['roles'],
            });
            if (!user) {
                await this.logSecurityEvent('LOGIN_ATTEMPT_INVALID_EMAIL', undefined, email);
                throw new common_1.UnauthorizedException('该邮箱地址未注册');
            }
            if (!user.isActive) {
                await this.logSecurityEvent('LOGIN_ATTEMPT_INACTIVE_ACCOUNT', user.id, email);
                throw new common_1.UnauthorizedException('账户已被禁用');
            }
            if (user.isLocked) {
                await this.logSecurityEvent('LOGIN_ATTEMPT_LOCKED_ACCOUNT', user.id, email);
                throw new common_1.UnauthorizedException('账户已被锁定');
            }
            if (user.failedLoginAttempts > 0) {
                await this.userRepository.update(user.id, {
                    login_attempts: 0,
                    locked_until: null,
                });
            }
            await this.userRepository.update(user.id, {
                last_login: new Date(),
            });
            const tokens = await this.generateTokens(user);
            await this.createUserSession(user, tokens.refreshToken);
            await this.logSecurityEvent('EMAIL_LOGIN_SUCCESS', user.id, email);
            const permissions = this.getUserPermissions(user);
            this.logger.log(`Email login successful for user: ${user.username}`);
            return {
                accessToken: tokens.accessToken,
                refreshToken: tokens.refreshToken,
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    fullName: user.fullName,
                    roles: user.roles.map(role => role.name),
                    permissions,
                    isActive: user.isActive,
                },
                expiresIn: 3600,
            };
        }
        catch (error) {
            this.logger.error(`Error during email login: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(user_session_entity_1.UserSession)),
    __param(2, (0, typeorm_1.InjectRepository)(audit_log_entity_1.AuditLog)),
    __param(3, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        jwt_1.JwtService,
        mail_service_1.MailService])
], AuthService);
//# sourceMappingURL=auth.service.js.map