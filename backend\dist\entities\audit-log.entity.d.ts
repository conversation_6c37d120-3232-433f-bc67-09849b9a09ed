import { User } from './user.entity';
export declare class AuditLog {
    log_id: string;
    user_id: string;
    action: string;
    resource: string;
    resource_id: string;
    old_values: any;
    new_values: any;
    ip_address: string;
    user_agent: string;
    details: string;
    resourceType: string;
    created_at: Date;
    user: User;
    get id(): string;
    get userId(): string;
    set userId(value: string);
    get resourceId(): string;
    set resourceId(value: string);
    get oldValues(): any;
    set oldValues(value: any);
    get newValues(): any;
    set newValues(value: any);
    get ipAddress(): string;
    set ipAddress(value: string);
    get userAgent(): string;
    set userAgent(value: string);
    get timestamp(): Date;
    static createLog(data: {
        user_id?: string;
        action: string;
        resource?: string;
        resource_id?: string;
        old_values?: any;
        new_values?: any;
        ip_address?: string;
        user_agent?: string;
    }): AuditLog;
    toSafeObject(): {
        log_id: string;
        user_id: string;
        action: string;
        resource: string;
        resource_id: string;
        old_values: any;
        new_values: any;
        ip_address: string;
        user_agent: string;
        created_at: Date;
        user: {
            user_id: string;
            username: string;
            full_name: string;
        } | null;
    };
}
