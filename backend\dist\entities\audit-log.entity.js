"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuditLog_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditLog = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("./user.entity");
let AuditLog = AuditLog_1 = class AuditLog {
    log_id;
    user_id;
    action;
    resource;
    resource_id;
    old_values;
    new_values;
    ip_address;
    user_agent;
    details;
    resourceType;
    created_at;
    user;
    get id() {
        return this.log_id;
    }
    get userId() {
        return this.user_id;
    }
    set userId(value) {
        this.user_id = value;
    }
    get resourceId() {
        return this.resource_id;
    }
    set resourceId(value) {
        this.resource_id = value;
    }
    get oldValues() {
        return this.old_values;
    }
    set oldValues(value) {
        this.old_values = value;
    }
    get newValues() {
        return this.new_values;
    }
    set newValues(value) {
        this.new_values = value;
    }
    get ipAddress() {
        return this.ip_address;
    }
    set ipAddress(value) {
        this.ip_address = value;
    }
    get userAgent() {
        return this.user_agent;
    }
    set userAgent(value) {
        this.user_agent = value;
    }
    get timestamp() {
        return this.created_at;
    }
    static createLog(data) {
        const log = new AuditLog_1();
        Object.assign(log, data);
        return log;
    }
    toSafeObject() {
        return {
            log_id: this.log_id,
            user_id: this.user_id,
            action: this.action,
            resource: this.resource,
            resource_id: this.resource_id,
            old_values: this.old_values,
            new_values: this.new_values,
            ip_address: this.ip_address,
            user_agent: this.user_agent,
            created_at: this.created_at,
            user: this.user ? {
                user_id: this.user.user_id,
                username: this.user.username,
                full_name: this.user.full_name,
            } : null,
        };
    }
};
exports.AuditLog = AuditLog;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AuditLog.prototype, "log_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], AuditLog.prototype, "user_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100 }),
    __metadata("design:type", String)
], AuditLog.prototype, "action", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], AuditLog.prototype, "resource", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], AuditLog.prototype, "resource_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], AuditLog.prototype, "old_values", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], AuditLog.prototype, "new_values", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'inet', nullable: true }),
    __metadata("design:type", String)
], AuditLog.prototype, "ip_address", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AuditLog.prototype, "user_agent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AuditLog.prototype, "details", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, nullable: true }),
    __metadata("design:type", String)
], AuditLog.prototype, "resourceType", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamptz' }),
    __metadata("design:type", Date)
], AuditLog.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.audit_logs),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], AuditLog.prototype, "user", void 0);
exports.AuditLog = AuditLog = AuditLog_1 = __decorate([
    (0, typeorm_1.Entity)('audit_logs', { schema: 'system' })
], AuditLog);
//# sourceMappingURL=audit-log.entity.js.map