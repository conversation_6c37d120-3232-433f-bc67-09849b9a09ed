import * as Cesium from "cesium"
import type { 
  GeoFeature, 
  FeatureType, 
  DrawStyle, 
  GeoEventCallbacks,
  EditState,
  GeoPoint,
  FeatureProperties
} from '../core/types'
import { EnhancedDrawTools } from '../drawing/drawTools'
import { DirectSelectionTool } from '../editing/enhancedDirectSelectionTool'
import { HistoryManager } from '../editing/historyManager'
import { GeometryEditTool } from '../editing/enhancedGeometryEditTool'
import { SplitTool } from '../editing/splitTool'
import { MergeTool } from '../editing/mergeTool'
import { geoEventBus, GEO_EVENTS } from '../core/eventBus'
import { KeyboardManager } from './keyboardManager'

/**
 * 地理编辑管理器
 * 统一管理绘图、编辑、选择等功能
 */
export class GeoManager {
  private viewer: Cesium.Viewer
  private drawTools: EnhancedDrawTools
  private selectionTool: DirectSelectionTool
  private historyManager: HistoryManager
  private geometryEditor: GeometryEditTool
  private splitTool: SplitTool
  private mergeTool: MergeTool
  private keyboardManager: KeyboardManager

  private features: GeoFeature[] = []
  private currentTool: string | null = null
  private editState: EditState = {
    isEditing: false,
    editingFeature: null,
    editMode: null  }  
  private callbacks: GeoEventCallbacks = {}
  
  constructor(viewer: Cesium.Viewer) {
    this.viewer = viewer    // 初始化各个工具
    this.drawTools = new EnhancedDrawTools(viewer)
    this.selectionTool = new DirectSelectionTool(viewer)
    this.historyManager = new HistoryManager()
    this.geometryEditor = new GeometryEditTool(viewer)
    this.splitTool = new SplitTool(viewer, this) // 传入this引用
    this.mergeTool = new MergeTool(viewer)
    this.keyboardManager = new KeyboardManager()

    this.setupEventListeners()
    this.setupToolCommunication()
    this.setupKeyboardShortcuts()

    console.log('🚀 地理编辑管理器已初始化')
  }

  // ==================== 公共访问器 ====================
  
  /**
   * 获取 Cesium Viewer 实例
   */
  get cesiumViewer(): Cesium.Viewer {
    return this.viewer
  }

  // ==================== 绘图相关方法 ====================

  /**
   * 开始绘制
   */
  startDraw(type: FeatureType) {
    this.deactivateCurrentTool()
    this.currentTool = 'draw'
    this.drawTools.startDraw(type)
  }

  /**
   * 停止绘制
   */
  stopDraw() {
    this.drawTools.stopDraw()
    this.currentTool = null
  }

  /**
   * 设置绘图样式
   */
  setDrawStyle(style: Partial<DrawStyle>) {
    this.drawTools.setDrawStyle(style)
  }

  // ==================== 选择相关方法 ====================
  /**
   * 激活选择工具
   */
  activateSelection(mode: 'single' | 'multiple' = 'single') {
    console.log(`🎯 GeoManager: 开始激活选择工具，模式: ${mode}`)
    console.log(`🎯 GeoManager: 当前工具: ${this.currentTool}`)
    console.log(`🎯 GeoManager: 选择工具实例:`, this.selectionTool)

    this.deactivateCurrentTool()
    this.currentTool = 'selection'

    try {
      this.selectionTool.activate()
      console.log(`✅ GeoManager: 选择工具激活成功`)
    } catch (error) {
      console.error(`❌ GeoManager: 选择工具激活失败:`, error)
    }

    console.log(`🎯 GeoManager: 激活选择工具完成，模式: ${mode}`)
  }
  /**
   * 停用选择工具
   */
  deactivateSelection(clearSelection = true) {
    this.selectionTool.deactivate(clearSelection)
    if (clearSelection) {
      this.currentTool = null
    }
  }
  /**
   * 选择要素
   */
  selectFeature(feature: GeoFeature, addToSelection: boolean = false) {
    // 暂时简单处理，等后续完善
    console.log('选择要素:', feature, '多选:', addToSelection)
  }

  /**
   * 通过ID选择要素
   */
  selectById(featureId: string, addToSelection: boolean = false) {
    // 暂时简单处理，等后续完善
    console.log('通过ID选择要素:', featureId, '多选:', addToSelection)
  }

  /**
   * 清除选择
   */
  clearSelection() {
    console.log('🧹 GeoManager: 清除选择')
    this.selectionTool.clearSelectionPublic?.() || this.selectionTool.clearSelection?.()
  }

  /**
   * 获取选中的要素
   */
  getSelectedFeatures(): GeoFeature[] {
    return this.selectionTool.getSelectedFeatures()
  }

  // ==================== 要素管理方法 ====================

  /**
   * 添加要素
   */
  addFeature(feature: GeoFeature) {
    this.features.push(feature)
    this.drawTools.addDrawResult(feature)
  }

  /**
   * 更新要素
   */
  updateFeature(feature: GeoFeature) {
    const index = this.features.findIndex(f => f.id === feature.id)
    if (index !== -1) {
      this.features[index] = feature
      this.drawTools.updateDrawing(feature)
    }
  }

  /**
   * 删除要素
   */
  deleteFeature(featureId: string) {
    const index = this.features.findIndex(f => f.id === featureId)
    if (index !== -1) {
      const feature = this.features[index]
      this.features.splice(index, 1)

      // 从Cesium场景中删除实体
      if (feature.entity) {
        this.viewer.entities.remove(feature.entity)
        console.log(`🗑️ 从场景中删除实体: ${featureId}`)
      }

      // 从绘图工具中删除
      this.drawTools.deleteDrawing(featureId)

      // 触发删除事件
      geoEventBus.emit(GEO_EVENTS.FEATURE_DELETED, featureId)
      console.log(`🗑️ 删除要素: ${featureId}`)

      return feature
    }
    console.warn(`⚠️ 未找到要删除的要素: ${featureId}`)
    return null
  }

  /**
   * 删除选中的要素
   */
  deleteSelectedFeatures() {
    const selectedFeatures = this.getSelectedFeatures()
    if (selectedFeatures.length === 0) {
      console.log('⚠️ 没有选中的要素可删除')
      return []
    }

    const deletedFeatures: GeoFeature[] = []
    selectedFeatures.forEach(feature => {
      const deleted = this.deleteFeature(feature.id)
      if (deleted) {
        deletedFeatures.push(deleted)
      }
    })

    // 清除选择
    this.clearSelection()

    console.log(`🗑️ 删除了 ${deletedFeatures.length} 个选中要素`)
    return deletedFeatures
  }

  /**
   * 获取所有要素
   */
  getFeatures(): GeoFeature[] {
    return [...this.features]
  }

  /**
   * 根据ID获取要素
   */
  getFeatureById(id: string): GeoFeature | undefined {
    return this.features.find(f => f.id === id)
  }

  /**
   * 根据类型获取要素
   */
  getFeaturesByType(type: FeatureType): GeoFeature[] {
    return this.features.filter(f => f.type === type)
  }

  /**
   * 清除所有要素
   */
  clearAllFeatures() {    this.features = []
    this.drawTools.clearAllDrawings()
    // this.selectionTool.clearSelection() // 暂时注释
  }

  // ==================== 历史管理方法 ====================

  /**
   * 撤销操作
   */
  undo() {
    const historyItem = this.historyManager.undo()
    if (historyItem) {
      this.applyHistoryItem(historyItem, 'undo')
    }
    return historyItem
  }

  /**
   * 重做操作
   */
  redo() {
    const historyItem = this.historyManager.redo()
    if (historyItem) {
      this.applyHistoryItem(historyItem, 'redo')
    }
    return historyItem
  }

  /**
   * 检查是否可以撤销
   */
  canUndo(): boolean {
    return this.historyManager.canUndo()
  }

  /**
   * 检查是否可以重做
   */
  canRedo(): boolean {
    return this.historyManager.canRedo()
  }

  /**
   * 获取历史记录
   */
  getHistory() {
    return this.historyManager.getHistory()
  }

  /**
   * 获取历史统计
   */
  getHistoryStats() {
    return this.historyManager.getHistoryStats()
  }
  // ==================== 编辑相关方法 ====================

  /**
   * 开始编辑要素几何
   */
  startGeometryEdit(feature: GeoFeature) {
    this.deactivateCurrentTool()
    this.currentTool = 'geometryEdit'
    this.geometryEditor.startEdit(feature)
    
    this.editState = {
      isEditing: true,
      editingFeature: feature,
      editMode: 'geometry'
    }

    // 触发事件
    this.callbacks.onEditStart?.(feature)
    geoEventBus.emit(GEO_EVENTS.EDIT_START, feature)

    console.log(`✏️ 开始几何编辑: ${feature.id}`)
  }

  /**
   * 停止几何编辑
   */
  stopGeometryEdit() {
    this.geometryEditor.stopEdit()
    this.currentTool = null
    
    this.editState = {
      isEditing: false,
      editingFeature: null,
      editMode: null
    }
  }

  /**
   * 激活分割工具
   */
  activateSplitTool() {
    this.deactivateCurrentTool()
    this.currentTool = 'split'
    this.splitTool.activate()
  }

  /**
   * 停用分割工具
   */
  deactivateSplitTool() {
    this.splitTool.deactivate()
    this.currentTool = null
  }

  /**
   * 激活合并工具
   */
  activateMergeTool() {
    this.deactivateCurrentTool()
    this.currentTool = 'merge'
    this.mergeTool.activate()
  }

  /**
   * 停用合并工具
   */
  deactivateMergeTool() {
    this.mergeTool.deactivate()
    this.currentTool = null
  }

  /**
   * 分割要素
   */
  splitFeature(feature: GeoFeature, splitPoint: Cesium.Cartesian3): GeoFeature[] | null {
    return this.splitTool.splitByClick(feature, splitPoint)
  }

  /**
   * 合并要素
   */
  mergeFeatures(features: GeoFeature[]): GeoFeature | null {
    // 清除当前选择
    this.mergeTool.clearSelection()
    
    // 添加要素到合并列表
    for (const feature of features) {
      this.mergeTool.addFeatureToSelection(feature)
    }
    
    // 执行合并
    return this.mergeTool.performMerge()
  }

  /**
   * 开始编辑要素
   */
  startEdit(feature: GeoFeature, mode: 'geometry' | 'properties') {
    if (mode === 'geometry') {
      this.startGeometryEdit(feature)
    } else {
      this.editState = {
        isEditing: true,
        editingFeature: feature,
        editMode: mode
      }

      // 自动选择要素
      this.selectFeature(feature)

      // 触发事件
      this.callbacks.onEditStart?.(feature)
      geoEventBus.emit(GEO_EVENTS.EDIT_START, feature)

      console.log(`✏️ 开始编辑要素: ${feature.id} (${mode})`)
    }
  }

  /**
   * 停止编辑
   */
  stopEdit() {
    const editingFeature = this.editState.editingFeature

    if (this.editState.editMode === 'geometry') {
      this.stopGeometryEdit()
    }

    this.editState = {
      isEditing: false,
      editingFeature: null,
      editMode: null
    }

    // 触发事件
    this.callbacks.onEditStop?.()
    geoEventBus.emit(GEO_EVENTS.EDIT_STOP)

    if (editingFeature) {
      console.log(`✅ 停止编辑要素: ${editingFeature.id}`)
    }
  }

  /**
   * 获取编辑状态
   */
  getEditState(): EditState {
    return { ...this.editState }
  }

  // ==================== 导入导出方法 ====================

  /**
   * 导出要素为GeoJSON
   */
  exportToGeoJSON(): string {
    const geoJSON = {
      type: 'FeatureCollection',
      features: this.features.map(feature => ({
        type: 'Feature',
        id: feature.id,
        geometry: this.convertToGeoJSONGeometry(feature),
        properties: {
          ...feature.properties,
          timestamp: feature.timestamp.toISOString()
        }
      }))
    }

    return JSON.stringify(geoJSON, null, 2)
  }

  /**
   * 从GeoJSON导入要素
   */
  importFromGeoJSON(geoJSON: string): boolean {
    try {
      const data = JSON.parse(geoJSON)
      
      if (data.type !== 'FeatureCollection' || !Array.isArray(data.features)) {
        throw new Error('Invalid GeoJSON format')
      }

      const importedFeatures: GeoFeature[] = []

      data.features.forEach((geoFeature: any) => {
        const feature = this.convertFromGeoJSONFeature(geoFeature)
        if (feature) {
          importedFeatures.push(feature)
        }
      })

      // 添加到要素列表
      importedFeatures.forEach(feature => {
        this.addFeature(feature)
      })

      console.log(`📥 导入了 ${importedFeatures.length} 个要素`)
      return true
    } catch (error) {
      console.error('导入GeoJSON失败:', error)
      return false
    }
  }

  // ==================== 工具状态管理 ====================

  /**
   * 获取当前激活的工具
   */
  getCurrentTool(): string | null {
    return this.currentTool
  }

  /**
   * 获取Cesium视图器
   */
  getViewer(): Cesium.Viewer {
    return this.viewer
  }
  /**
   * 停用当前工具
   */
  deactivateCurrentTool() {
    switch (this.currentTool) {
      case 'draw':
        this.stopDraw()
        break
      case 'selection':
        this.deactivateSelection()
        break
      case 'geometryEdit':
        this.stopGeometryEdit()
        break
      case 'split':
        this.deactivateSplitTool()
        break
      case 'merge':
        this.deactivateMergeTool()
        break
      case 'edit':
        this.stopEdit()
        break
    }
  }

  // ==================== 键盘快捷键管理 ====================

  /**
   * 设置键盘快捷键
   */
  private setupKeyboardShortcuts() {
    // Delete键 - 删除选中要素
    this.keyboardManager.registerShortcut({
      key: 'Delete',
      description: '删除选中的要素',
      action: () => {
        console.log('🔥 Delete键被按下!')
        console.log('🔍 当前工具:', this.currentTool)
        console.log('🔍 选中要素数量:', this.getSelectedFeatures().length)
        console.log('🔍 选中要素:', this.getSelectedFeatures())

        if (this.currentTool === 'selection' || this.getSelectedFeatures().length > 0) {
          console.log('✅ 条件满足，执行删除操作')
          this.deleteSelectedFeatures()
        } else {
          console.log('❌ 条件不满足，无法删除')
        }
      }
    })

    // Escape键 - 取消当前操作
    this.keyboardManager.registerShortcut({
      key: 'Escape',
      description: '取消当前操作',
      action: () => {
        this.cancelCurrentOperation()
      }
    })

    // Ctrl+Z - 撤销
    this.keyboardManager.registerShortcut({
      key: 'z',
      ctrlKey: true,
      description: '撤销操作',
      action: () => {
        this.undo()
      }
    })

    // Ctrl+Y - 重做
    this.keyboardManager.registerShortcut({
      key: 'y',
      ctrlKey: true,
      description: '重做操作',
      action: () => {
        this.redo()
      }
    })

    console.log('⌨️ 键盘快捷键已设置')
  }

  /**
   * 激活键盘快捷键
   */
  activateKeyboardShortcuts() {
    console.log('🔥 GeoManager: 开始激活键盘快捷键')
    console.log('🔥 键盘管理器实例:', this.keyboardManager)
    console.log('🔥 已注册的快捷键数量:', this.keyboardManager ? this.keyboardManager.getShortcuts().size : 0)

    this.keyboardManager.activate()
    console.log('🔥 GeoManager: 键盘快捷键激活完成')
  }

  /**
   * 停用键盘快捷键
   */
  deactivateKeyboardShortcuts() {
    this.keyboardManager.deactivate()
  }

  /**
   * 取消当前操作
   */
  private cancelCurrentOperation() {
    console.log('🚫 取消当前操作:', this.currentTool)

    switch (this.currentTool) {
      case 'selection':
        this.deactivateSelection()
        break
      case 'draw':
        this.stopDraw()
        break
      case 'geometryEdit':
        this.stopGeometryEdit()
        break
      case 'split':
        this.deactivateSplitTool()
        break
      case 'merge':
        this.deactivateMergeTool()
        break
      default:
        // 如果没有激活的工具，清除选择
        this.selectionTool.clearSelection?.()
        break
    }
  }

  // ==================== 事件和回调管理 ====================

  /**
   * 设置事件回调
   */  setEventCallbacks(callbacks: GeoEventCallbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks }
      // 传递给子工具    this.drawTools.setEventCallbacks(callbacks)
    // this.selectionTool.setEventCallbacks(callbacks) // 暂时注释
    this.historyManager.setEventCallbacks(callbacks)
    // this.geometryEditor.setEventCallbacks(callbacks) // 新版本不需要
    this.splitTool.setEventCallbacks(callbacks)
    this.mergeTool.setEventCallbacks(callbacks)
  }

  // ==================== 私有方法 ====================

  /**
   * 设置事件监听器
   */  private setupEventListeners() {
    // 监听要素创建
    geoEventBus.on(GEO_EVENTS.FEATURE_CREATED, (feature: GeoFeature) => {
      this.features.push(feature)
    })

    // 监听要素删除
    geoEventBus.on(GEO_EVENTS.FEATURE_DELETED, (featureId: string) => {
      const index = this.features.findIndex(f => f.id === featureId)
      if (index !== -1) {
        this.features.splice(index, 1)
      }
    })

    // 监听分割操作完成
    geoEventBus.on('split:completed', (data: { originalFeature: GeoFeature, splitFeatures: GeoFeature[] }) => {
      console.log('🎯 GeoManager: 接收到分割完成事件', data)
      
      // 删除原始要素
      const originalIndex = this.features.findIndex(f => f.id === data.originalFeature.id)
      if (originalIndex !== -1) {
        this.features.splice(originalIndex, 1)
        this.drawTools.deleteDrawing(data.originalFeature.id)
        console.log('✅ 删除原始要素:', data.originalFeature.id)
      }
      
      // 添加分割后的要素
      data.splitFeatures.forEach(feature => {
        this.features.push(feature)
        this.drawTools.addDrawResult(feature)
        console.log('✅ 添加分割要素:', feature.id)
      })
      
      console.log(`🎉 分割操作完成: 1 -> ${data.splitFeatures.length}`)
    })
  }

  /**
   * 设置工具间通信
   */
  private setupToolCommunication() {
    // 选择工具请求要素列表
    geoEventBus.on('selection:requestFeatures', (_position: Cesium.Cartesian2) => {
      // this.selectionTool.selectByPosition(position, this.features) // 暂时注释
    })
  }

  /**
   * 应用历史记录项
   */
  private applyHistoryItem(historyItem: any, direction: 'undo' | 'redo') {
    // 实现历史记录的应用逻辑
    console.log(`应用历史记录: ${historyItem.description} (${direction})`)
  }

  /**
   * 转换为GeoJSON几何
   */
  private convertToGeoJSONGeometry(feature: GeoFeature): any {
    // 实现GeoJSON几何转换
    switch (feature.type) {
      case 'point':
        return {
          type: 'Point',
          coordinates: [feature.points[0].longitude, feature.points[0].latitude]
        }
      case 'line':
        return {
          type: 'LineString',
          coordinates: feature.points.map(p => [p.longitude, p.latitude])
        }
      case 'polygon':
        return {
          type: 'Polygon',
          coordinates: [feature.points.map(p => [p.longitude, p.latitude])]
        }
      default:
        return null
    }
  }
  /**
   * 从GeoJSON要素转换
   */
  private convertFromGeoJSONFeature(_geoFeature: any): GeoFeature | null {
    // 实现从GeoJSON要素转换的逻辑
    // 这里是简化实现
    return null
  }
  /**
   * 销毁管理器
   */
  destroy() {
    this.deactivateCurrentTool()
    this.drawTools.destroy()
    // this.selectionTool.destroy() // 暂时注释，新工具没有这个方法
    this.selectionTool.destroy() // 新工具现在有这个方法了
    this.historyManager.destroy()
    this.geometryEditor.destroy()
    this.splitTool.destroy()
    this.mergeTool.destroy()
    this.features = []
    
    console.log('🗑️ 地理编辑管理器已销毁')
  }

  // ==================== 图层数据管理方法 ====================

  /**
   * 从 Cesium 场景中加载现有的图层数据
   */
  loadLayerData() {
    console.log('🔄 正在加载图层数据...')
    
    // 清空现有要素
    this.features = []
    
    // 遍历所有实体并转换为要素
    this.viewer.entities.values.forEach(entity => {
      if (entity.id && entity.id.startsWith('temp_')) {
        // 跳过临时实体
        return
      }
      
      const feature = this.convertEntityToFeature(entity)
      if (feature) {
        this.features.push(feature)
      }
    })
    
    console.log(`✅ 加载了 ${this.features.length} 个要素`)
    
    // 触发数据加载完成事件
    geoEventBus.emit(GEO_EVENTS.DATA_LOADED, this.features)
    
    return this.features
  }

  /**
   * 将 Cesium 实体转换为 GeoFeature
   */
  private convertEntityToFeature(entity: Cesium.Entity): GeoFeature | null {
    if (!entity) return null

    // 提取坐标点
    const points = this.extractEntityPoints(entity)
    if (!points || points.length === 0) return null

    // 获取要素类型
    const type = this.getEntityType(entity)

    // 基础要素信息
    const feature: GeoFeature = {
      id: entity.id,
      type: type,
      points: points,
      properties: {
        name: entity.name || `${type}_${entity.id}`,
        description: entity.description?.getValue(Cesium.JulianDate.now()) || '',
        ...this.extractEntityStyleProperties(entity)
      },
      timestamp: new Date(),
      entity: entity
    }

    return feature
  }

  /**
   * 提取实体的坐标点
   */
  private extractEntityPoints(entity: Cesium.Entity): GeoPoint[] {
    const points: GeoPoint[] = []

    if (entity.position) {
      const position = entity.position.getValue(Cesium.JulianDate.now())
      if (position) {
        const cartographic = Cesium.Cartographic.fromCartesian(position)
        points.push({
          longitude: Cesium.Math.toDegrees(cartographic.longitude),
          latitude: Cesium.Math.toDegrees(cartographic.latitude),
          height: cartographic.height
        })
      }
    }
    
    if (entity.polyline?.positions) {
      const positions = entity.polyline.positions.getValue(Cesium.JulianDate.now())
      if (positions) {
        positions.forEach((pos: Cesium.Cartesian3) => {
          const cartographic = Cesium.Cartographic.fromCartesian(pos)
          points.push({
            longitude: Cesium.Math.toDegrees(cartographic.longitude),
            latitude: Cesium.Math.toDegrees(cartographic.latitude),
            height: cartographic.height
          })
        })
      }
    }
    
    if (entity.polygon?.hierarchy) {
      const hierarchy = entity.polygon.hierarchy.getValue(Cesium.JulianDate.now())
      if (hierarchy) {
        const positions = Array.isArray(hierarchy) ? hierarchy : hierarchy.positions
        if (positions) {
          positions.forEach((pos: Cesium.Cartesian3) => {
            const cartographic = Cesium.Cartographic.fromCartesian(pos)
            points.push({
              longitude: Cesium.Math.toDegrees(cartographic.longitude),
              latitude: Cesium.Math.toDegrees(cartographic.latitude),
              height: cartographic.height
            })
          })
        }
      }
    }
    
    return points
  }

  /**
   * 获取实体的几何类型
   */
  private getEntityType(entity: Cesium.Entity): FeatureType {
    if (entity.point) return 'point'
    if (entity.polyline) return 'line'
    if (entity.polygon) return 'polygon'
    if (entity.ellipse) return 'circle'
    if (entity.rectangle) return 'rectangle'
    return 'point' // 默认类型
  }

  /**
   * 提取实体的样式属性
   */
  private extractEntityStyleProperties(entity: Cesium.Entity): Partial<FeatureProperties> {
    const styleProps: Partial<FeatureProperties> = {}
    
    if (entity.point) {
      const pointSize = entity.point.pixelSize?.getValue(Cesium.JulianDate.now())
      const pointColor = entity.point.color?.getValue(Cesium.JulianDate.now())
      if (pointSize) styleProps.strokeWidth = pointSize
      if (pointColor) styleProps.color = pointColor.toCssColorString()
    }
    
    if (entity.polyline) {
      const lineWidth = entity.polyline.width?.getValue(Cesium.JulianDate.now())
      const lineMaterial = entity.polyline.material?.getValue(Cesium.JulianDate.now())
      if (lineWidth) styleProps.strokeWidth = lineWidth
      if (lineMaterial && lineMaterial.color) {
        styleProps.strokeColor = lineMaterial.color.toCssColorString()
      }
    }
    
    if (entity.polygon) {
      const fillMaterial = entity.polygon.material?.getValue(Cesium.JulianDate.now())
      const outlineColor = entity.polygon.outlineColor?.getValue(Cesium.JulianDate.now())
      if (fillMaterial && fillMaterial.color) {
        styleProps.fillColor = fillMaterial.color.toCssColorString()
        styleProps.fillOpacity = fillMaterial.color.alpha      }
      if (outlineColor) {
        styleProps.strokeColor = outlineColor.toCssColorString()
      }
    }
    
    return styleProps
  }

  /**
   * 创建测试数据（用于验证选择功能）
   */
  createTestData() {
    console.log('🧪 创建测试数据')
    
    // 创建一个点要素
    const pointEntity = this.viewer.entities.add({
      id: 'test-point-1',
      name: '测试点',
      position: Cesium.Cartesian3.fromDegrees(116.3974, 39.9093, 1000),
      point: {
        pixelSize: 10,
        color: Cesium.Color.RED,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    })
    
    // 创建一个线要素
    const lineEntity = this.viewer.entities.add({
      id: 'test-line-1',
      name: '测试线',
      polyline: {
        positions: Cesium.Cartesian3.fromDegreesArray([
          116.3974, 39.9093,
          116.4074, 39.9193,
          116.4174, 39.9093
        ]),
        width: 3,
        material: Cesium.Color.BLUE,
        clampToGround: true
      }
    })
    
    // 创建一个多边形要素
    const polygonEntity = this.viewer.entities.add({
      id: 'test-polygon-1',
      name: '测试多边形',
      polygon: {
        hierarchy: Cesium.Cartesian3.fromDegreesArray([
          116.3874, 39.8993,
          116.3974, 39.8993,
          116.3974, 39.9093,
          116.3874, 39.9093
        ]),
        material: Cesium.Color.GREEN.withAlpha(0.5),
        outline: true,
        outlineColor: Cesium.Color.GREEN,
        height: 0
      }
    })
    
    console.log('✅ 测试数据创建完成:', { pointEntity, lineEntity, polygonEntity })
    
    // 缩放到测试数据
    this.viewer.zoomTo(this.viewer.entities)
    
    return { pointEntity, lineEntity, polygonEntity }
  }
}