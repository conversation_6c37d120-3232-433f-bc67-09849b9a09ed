"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserSession = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("./user.entity");
let UserSession = class UserSession {
    session_id;
    user_id;
    refresh_token;
    ip_address;
    user_agent;
    expires_at;
    created_at;
    last_used_at;
    user;
    password;
    get id() {
        return this.session_id;
    }
    get userId() {
        return this.user_id;
    }
    set userId(value) {
        this.user_id = value;
    }
    get refreshToken() {
        return this.refresh_token;
    }
    set refreshToken(value) {
        this.refresh_token = value;
    }
    get ipAddress() {
        return this.ip_address;
    }
    set ipAddress(value) {
        this.ip_address = value;
    }
    get userAgent() {
        return this.user_agent;
    }
    set userAgent(value) {
        this.user_agent = value;
    }
    get expiresAt() {
        return this.expires_at;
    }
    set expiresAt(value) {
        this.expires_at = value;
    }
    get createdAt() {
        return this.created_at;
    }
    get lastUsedAt() {
        return this.last_used_at;
    }
    set lastUsedAt(value) {
        this.last_used_at = value;
    }
    get isActive() {
        return !this.isExpired();
    }
    get loggedOutAt() {
        return this.isExpired() ? this.expires_at : null;
    }
    isExpired() {
        return new Date() > this.expires_at;
    }
    updateLastUsed() {
        this.last_used_at = new Date();
    }
    toSafeObject() {
        return {
            session_id: this.session_id,
            user_id: this.user_id,
            ip_address: this.ip_address,
            user_agent: this.user_agent,
            expires_at: this.expires_at,
            created_at: this.created_at,
            last_used_at: this.last_used_at,
            is_expired: this.isExpired(),
        };
    }
};
exports.UserSession = UserSession;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], UserSession.prototype, "session_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], UserSession.prototype, "user_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 500 }),
    __metadata("design:type", String)
], UserSession.prototype, "refresh_token", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'inet', nullable: true }),
    __metadata("design:type", String)
], UserSession.prototype, "ip_address", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], UserSession.prototype, "user_agent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamptz' }),
    __metadata("design:type", Date)
], UserSession.prototype, "expires_at", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ type: 'timestamptz' }),
    __metadata("design:type", Date)
], UserSession.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ type: 'timestamptz' }),
    __metadata("design:type", Date)
], UserSession.prototype, "last_used_at", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.sessions, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], UserSession.prototype, "user", void 0);
exports.UserSession = UserSession = __decorate([
    (0, typeorm_1.Entity)('user_sessions', { schema: 'system' })
], UserSession);
//# sourceMappingURL=user-session.entity.js.map