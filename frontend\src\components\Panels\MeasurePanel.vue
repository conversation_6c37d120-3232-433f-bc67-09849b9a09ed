<template>
  <div class="measure-panel-content">

    <!-- 工具选择 -->
    <div class="tool-selection">
      <el-button-group>
        <el-button 
          :type="activeMeasureType === 'distance' ? 'primary' : 'default'"
          size="small"
          @click="startMeasure('distance')"
        >
          <i class="fas fa-ruler"></i>
          距离
        </el-button>
        <el-button 
          :type="activeMeasureType === 'area' ? 'primary' : 'default'"
          size="small"
          @click="startMeasure('area')"
        >
          <i class="fas fa-draw-polygon"></i>
          面积
        </el-button>
        <el-button 
          :type="activeMeasureType === 'height' ? 'primary' : 'default'"
          size="small"
          @click="startMeasure('height')"
        >
          <i class="fas fa-mountain"></i>
          高程
        </el-button>
      </el-button-group>
    </div>

    <!-- 操作提示 -->
    <div v-if="activeMeasureType" class="measure-tips">
      <el-alert
        :title="getMeasureTip()"
        type="info"
        :closable="false"
        show-icon
      />
      <div class="tip-actions">
        <el-button size="small" @click="stopMeasure">
          <i class="fas fa-stop"></i>
          停止测量
        </el-button>
        <el-button size="small" @click="clearAll">
          <i class="fas fa-trash"></i>
          清除全部
        </el-button>
      </div>
    </div>

    <!-- 测量结果列表 -->
    <div class="results-section">
      <div class="section-header">
        <h4>测量结果</h4>
        <div class="header-actions">
          <el-button 
            size="small" 
            @click="showExportDialog = true"
            :disabled="measureResults.length === 0"
          >
            <i class="fas fa-download"></i>
            导出
          </el-button>
        </div>
      </div>

      <div class="results-list">
        <div 
          v-for="result in measureResults" 
          :key="result.id"
          class="result-item"
        >
          <div class="result-header">
            <div class="result-icon">
              <i :class="getResultIcon(result.type)"></i>
            </div>
            <div class="result-info">
              <div class="result-label">{{ result.label }}</div>
              <div class="result-time">{{ formatTime(result.timestamp) }}</div>
            </div>
            <div class="result-actions">
              <el-button 
                type="text" 
                size="small"
                @click="zoomToResult(result)"
                title="定位到结果"
              >
                <i class="fas fa-search-location"></i>
              </el-button>
              <el-button 
                type="text" 
                size="small"
                @click="deleteResult(result.id)"
                title="删除结果"
              >
                <i class="fas fa-trash"></i>
              </el-button>
            </div>
          </div>

          <!-- 详细信息 -->
          <div class="result-details">
            <div class="detail-item">
              <span class="detail-label">类型:</span>
              <span class="detail-value">{{ getTypeLabel(result.type) }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">数值:</span>
              <span class="detail-value">{{ formatValue(result.value, result.unit) }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">点数:</span>
              <span class="detail-value">{{ result.points.length }}个</span>
            </div>
          </div>
        </div>

        <div v-if="measureResults.length === 0" class="empty-state">
          <i class="fas fa-ruler"></i>
          <p>暂无测量结果</p>
          <p class="empty-tip">选择测量工具开始测量</p>
        </div>
      </div>
    </div>

    <!-- 导出对话框 -->
    <el-dialog v-model="showExportDialog" title="导出测量结果" width="400px">
      <el-form :model="exportForm" label-width="80px">
        <el-form-item label="文件名">
          <el-input 
            v-model="exportForm.filename" 
            placeholder="请输入文件名"
            suffix-icon="Document"
          />
        </el-form-item>
        <el-form-item label="格式">
          <el-select v-model="exportForm.format" style="width: 100%">
            <el-option label="JSON" value="json" />
            <el-option label="GeoJSON" value="geojson" />
            <el-option label="CSV" value="csv" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showExportDialog = false">取消</el-button>
        <el-button type="primary" @click="exportResults" :loading="exporting">
          导出
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { MeasureResult } from '../../utils/measureTools'

const props = defineProps<{
  measureResults: MeasureResult[]
  activeMeasureType: string | null
}>()

const emit = defineEmits<{
  close: []
  startMeasure: [type: 'distance' | 'area' | 'height']
  stopMeasure: []
  clearAll: []
  deleteResult: [id: string]
  zoomToResult: [result: MeasureResult]
  exportResults: [filename: string, format: string]
}>()



// 导出相关
const showExportDialog = ref(false)
const exporting = ref(false)
const exportForm = ref({
  filename: `measure_results_${new Date().toISOString().slice(0, 10)}`,
  format: 'json'
})

// 获取测量提示
const getMeasureTip = () => {
  switch (props.activeMeasureType) {
    case 'distance':
      return '单击地图添加测量点，右键或双击完成当前测量。支持连续测量多条线段。'
    case 'area':
      return '单击地图添加顶点，右键或双击闭合多边形。支持连续测量多个区域。'
    case 'height':
      return '单击地图获取该点高程信息。支持连续测量多个点的高程。'
    default:
      return ''
  }
}

// 获取结果图标
const getResultIcon = (type: string) => {
  switch (type) {
    case 'distance': return 'fas fa-ruler'
    case 'area': return 'fas fa-draw-polygon'
    case 'height': return 'fas fa-mountain'
    default: return 'fas fa-ruler'
  }
}

// 获取类型标签
const getTypeLabel = (type: string) => {
  switch (type) {
    case 'distance': return '距离测量'
    case 'area': return '面积测量'
    case 'height': return '高程测量'
    default: return '未知'
  }
}

// 格式化数值
const formatValue = (value: number, unit: string) => {
  if (unit === 'm' && value >= 1000) {
    return `${(value / 1000).toFixed(2)} km`
  } else if (unit === 'm²' && value >= 10000) {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(2)} km²`
    } else {
      return `${(value / 10000).toFixed(2)} 公顷`
    }
  }
  return `${value.toFixed(2)} ${unit}`
}

// 格式化时间
const formatTime = (timestamp: Date) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 开始测量
const startMeasure = (type: 'distance' | 'area' | 'height') => {
  emit('startMeasure', type)
}

// 停止测量
const stopMeasure = () => {
  emit('stopMeasure')
}

// 清除全部
const clearAll = () => {
  emit('clearAll')
}

// 删除结果
const deleteResult = (id: string) => {
  emit('deleteResult', id)
}

// 定位到结果
const zoomToResult = (result: MeasureResult) => {
  emit('zoomToResult', result)
}

// 导出结果
const exportResults = async () => {
  if (!exportForm.value.filename.trim()) {
    ElMessage.error('请输入文件名')
    return
  }

  exporting.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('exportResults', exportForm.value.filename, exportForm.value.format)
    
    ElMessage.success('导出成功')
    showExportDialog.value = false
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 面板初始化完成后可以在这里添加一些处理
onMounted(() => {
  console.log('测量面板已初始化')
})
</script>

<style scoped>
.measure-panel-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tool-selection {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.measure-tips {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.tip-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.results-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 16px 8px 16px;
}

.section-header h4 {
  margin: 0;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.results-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px 16px 16px;
}

.result-item {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 12px;
  overflow: hidden;
}

.result-header {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f8fafc;
}

.result-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 12px;
}

.result-info {
  flex: 1;
}

.result-label {
  font-weight: 500;
  color: #1f2937;
  font-size: 12px;
}

.result-time {
  font-size: 10px;
  color: #6b7280;
  margin-top: 2px;
}

.result-actions {
  display: flex;
  gap: 4px;
}

.result-details {
  padding: 12px;
  background: white;
  border-top: 1px solid #e5e7eb;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 11px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #6b7280;
}

.detail-value {
  color: #1f2937;
  font-weight: 500;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
  font-size: 0.75rem;
}

.empty-state i {
  font-size: 40px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state p {
  margin: 8px 0;
}

.empty-tip {
  font-size: 10px;
  opacity: 0.7;
}

/* 滚动条样式 */
.results-list::-webkit-scrollbar {
  width: 6px;
}

.results-list::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.results-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.results-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
