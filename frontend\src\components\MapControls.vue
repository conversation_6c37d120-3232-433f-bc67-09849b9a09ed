<template>
  <div class="map-controls">
    <button class="control-button !rounded-button" title="放大">
      <i class="fas fa-plus"></i>
    </button>
    <button class="control-button !rounded-button" title="缩小">
      <i class="fas fa-minus"></i>
    </button>
    <button class="control-button !rounded-button" title="当前位置">
      <i class="fas fa-location-dot"></i>
    </button>
    <button class="control-button !rounded-button" title="Home">
      <i class="fas fa-home"></i>
    </button>
    <button
      class="control-button !rounded-button"
      :class="{ 'quality-active': isHighQuality }"
      @click="toggleQuality"
      :title="isHighQuality ? '切换到高性能模式' : '切换到高质量模式'"
    >
      <i class="fas fa-eye" v-if="isHighQuality"></i>
      <i class="fas fa-tachometer-alt" v-else></i>
    </button>
    <button
      class="control-button !rounded-button"
      :class="{ 'terrain-active': isTerrainEnhanced }"
      @click="toggleTerrainEnhancement"
      :title="isTerrainEnhanced ? '关闭地形增强' : '启用地形增强'"
    >
      <i class="fas fa-mountain"></i>
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useMapStore } from '../stores/map'
import { ElMessage } from 'element-plus'

const mapStore = useMapStore()
const isHighQuality = ref(true) // 默认高质量模式
const isTerrainEnhanced = ref(false) // 地形增强状态

const toggleQuality = () => {
  const viewer = mapStore.viewer
  if (!viewer) {
    ElMessage.warning('地图未初始化')
    return
  }

  // 获取质量优化器（需要从CesiumViewer组件获取）
  const cesiumViewerComponent = document.querySelector('.cesium-container')?.__vueParentComponent?.exposed
  const qualityOptimizer = cesiumViewerComponent?.getQualityOptimizer?.()

  if (!qualityOptimizer) {
    ElMessage.warning('质量优化器未初始化')
    return
  }

  if (isHighQuality.value) {
    // 切换到高性能模式
    qualityOptimizer.applyHighPerformance()
    isHighQuality.value = false
    ElMessage.success('已切换到高性能模式')
  } else {
    // 切换到高质量模式
    qualityOptimizer.applyHighQuality()
    isHighQuality.value = true
    ElMessage.success('已切换到高质量模式')
  }
}

const toggleTerrainEnhancement = () => {
  const viewer = mapStore.viewer
  if (!viewer) {
    ElMessage.warning('地图未初始化')
    return
  }

  // 获取质量优化器
  const cesiumViewerComponent = document.querySelector('.cesium-container')?.__vueParentComponent?.exposed
  const qualityOptimizer = cesiumViewerComponent?.getQualityOptimizer?.()

  if (!qualityOptimizer) {
    ElMessage.warning('质量优化器未初始化')
    return
  }

  if (isTerrainEnhanced.value) {
    // 关闭地形增强，恢复标准地形
    qualityOptimizer.setTerrainExaggeration(1.0)
    viewer.scene.globe.enableLighting = false
    isTerrainEnhanced.value = false
    ElMessage.success('已关闭地形增强')
  } else {
    // 启用地形增强
    qualityOptimizer.enableTerrainEnhancement()
    isTerrainEnhanced.value = true
    ElMessage.success('已启用地形增强，3D效果更明显')
  }
}
</script>

<style scoped>
.map-controls {
  position: absolute;
  right: 32px;
  bottom: 32px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 20;
}
.control-button {
  width: 36px;
  height: 36px;
  background: rgba(255,255,255,0.9);
  border: none;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  color: #475569;
  font-size: 18px;
  cursor: pointer;
  outline: none;
  transition: background 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}
.control-button:hover {
  background: #1d4ed8;
  color: #fff;
}

.quality-active {
  background: #10b981 !important;
  color: #fff !important;
}

.quality-active:hover {
  background: #059669 !important;
}

.terrain-active {
  background: #f59e0b !important;
  color: #fff !important;
}

.terrain-active:hover {
  background: #d97706 !important;
}
</style>