<template>  <div class="geoedit-panel">
    <!-- 编辑工具区域 -->
    <div class="panel-section">
      <h3>编辑工具</h3>
      
      <!-- 工具状态提示 -->
      <div v-if="activeTool" class="tool-status" style="margin-bottom: 0.5rem; padding: 0.5rem; background: #e6f7ff; border: 1px solid #91d5ff; border-radius: 4px; font-size: 12px;">
        <div v-if="activeTool === 'select'" style="color: #0050b3;">
          🎯 <strong>选择模式已激活</strong><br>
          • 单击要素进行选择<br>
          • 拖拽画框进行批量选择<br>
          • 地图拖拽已禁用，完成后请点击"停用工具"
        </div>
        <div v-else-if="activeTool === 'geometry'" style="color: #0050b3;">
          ✏️ <strong>几何编辑模式已激活</strong>
        </div>
        <div v-else-if="activeTool === 'split'" style="color: #0050b3;">
          ✂️ <strong>分割模式已激活</strong>
        </div>
        <div v-else-if="activeTool === 'merge'" style="color: #0050b3;">
          🔗 <strong>合并模式已激活</strong>
        </div>
      </div>
      
      <div class="tool-grid">
        <button 
          v-for="tool in editTools" 
          :key="tool.id"
          class="tool-button"
          :class="{ active: activeTool === tool.id }"
          @click="selectTool(tool.id)"
        >
          <span class="icon">{{ tool.icon }}</span>
          {{ tool.name }}
        </button>
      </div>
        <!-- 停用工具按钮 -->
      <div class="tool-controls" style="margin-top: 0.5rem;">        <button 
          class="stop-tool-button"
          @click="deactivateAllTools"
          :disabled="!activeTool"
          style="width: 100%; padding: 0.5rem; background: #f0f0f0; color: #666; border: 1px solid #d9d9d9; border-radius: 4px; cursor: pointer;"
          :style="{ opacity: activeTool ? 1 : 0.5 }"
        >
          🛑 停用工具并恢复地图控制
        </button>
      </div>
    </div>

    <!-- 选中要素信息 -->
    <div class="panel-section" v-if="selectedFeatures.length > 0">
      <h3>选中要素 ({{ selectedFeatures.length }}个)</h3>
      <div class="selected-features">
        <div 
          v-for="feature in selectedFeatures" 
          :key="feature.id"
          class="feature-item"
          :class="{ editing: currentEditingFeature?.id === feature.id }"
          @click="setCurrentEditingFeature(feature)"
        >
          <div class="feature-info">
            <span class="feature-type">{{ getFeatureTypeName(feature.type) }}</span>
            <span class="feature-name">{{ feature.properties?.name || feature.id }}</span>
          </div>
          <div class="feature-actions">
            <button class="action-btn edit-btn" @click="startGeometryEdit(feature)" title="编辑几何">
              <span>✏️</span>
            </button>
            <button class="action-btn delete-btn" @click="deleteFeature(feature)" title="删除">
              <span>🗑️</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 要素属性编辑 -->
    <div class="panel-section" v-if="currentEditingFeature">
      <h3>编辑属性</h3>
      <div class="attribute-form">
        <div class="form-group">
          <label>要素ID</label>
          <input type="text" :value="currentEditingFeature.id" disabled>
        </div>
        <div class="form-group">
          <label>名称</label>
          <input 
            type="text" 
            v-model="currentEditingFeature.properties!.name"
            @input="updateFeatureProperty('name', ($event.target as HTMLInputElement).value)"
          >
        </div>
        <div class="form-group">
          <label>类型</label>
          <select 
            v-model="currentEditingFeature.type" 
            @change="updateFeatureProperty('geometryType', ($event.target as HTMLSelectElement).value)"
            disabled
          >
            <option value="point">点</option>
            <option value="line">线</option>
            <option value="polygon">面</option>
          </select>
        </div>
        <div class="form-group">
          <label>描述</label>
          <textarea 
            v-model="currentEditingFeature.properties!.description"
            @input="updateFeatureProperty('description', ($event.target as HTMLTextAreaElement).value)"
          ></textarea>
        </div>
        <div class="form-group">
          <label>样式</label>
          <div class="style-controls">
            <div class="color-control">
              <label>填充颜色</label>
              <input 
                type="color" 
                v-model="currentEditingFeature.properties!.fillColor"
                @change="updateFeatureStyle"
              >
            </div>
            <div class="color-control">
              <label>边框颜色</label>
              <input 
                type="color" 
                v-model="currentEditingFeature.properties!.strokeColor"
                @change="updateFeatureStyle"
              >
            </div>
          </div>
        </div>
        <div class="button-group">
          <button class="save-btn" @click="saveFeature">保存更改</button>
          <button class="cancel-btn" @click="cancelEdit">取消编辑</button>
        </div>
      </div>
    </div>

    <!-- 批量操作 -->
    <div class="panel-section" v-if="selectedFeatures.length > 1">
      <h3>批量操作</h3>
      <div class="batch-operations">
        <button class="batch-btn" @click="activateMergeTool" :disabled="!canMerge">
          <span class="icon">🔗</span>
          合并要素
        </button>
        <button class="batch-btn" @click="deleteBatch">
          <span class="icon">🗑️</span>
          批量删除
        </button>
        <button class="batch-btn" @click="clearSelection">
          <span class="icon">❌</span>
          清除选择
        </button>
      </div>
    </div>    <!-- 编辑历史 -->
    <div class="panel-section">
      <h3>编辑历史</h3>
      <div class="history-controls">
        <button class="history-btn" @click="undo" :disabled="!canUndo">
          <span class="icon">↶</span>
          撤销
        </button>
        <button class="history-btn" @click="redo" :disabled="!canRedo">
          <span class="icon">↷</span>
          重做
        </button>
      </div>
      <div class="history-list">
        <div v-if="historyList.length > 0">
          <div v-for="(history, index) in historyList.slice(0, 10)" 
               :key="index" 
               class="history-item"
          >
            <span class="history-action">{{ history.operation || history.action }}</span>
            <span class="history-time">{{ formatTime(history.timestamp) }}</span>
          </div>
        </div>
        <p v-else class="no-history">暂无编辑历史</p>
      </div>
    </div>

    <!-- 导出功能 -->
    <div class="panel-section">
      <h3>数据导出</h3>
      <div class="export-controls">
        <button class="export-btn" @click="showExportPanel = true" :disabled="!hasFeatures">
          <span class="icon">📁</span>
          导出数据
        </button>
        <p class="export-info">支持 GeoJSON、KML、Shapefile 格式</p>
      </div>
    </div>

    <!-- 高级编辑功能 -->
    <div class="panel-section">
      <h3>高级编辑</h3>
      <div class="advanced-edit-controls">
        <button class="advanced-edit-btn" @click="openQGIS" :disabled="isOpeningQGIS">
          <span class="icon">🗺️</span>
          <span v-if="!isOpeningQGIS">高级编辑</span>
          <span v-else>正在启动...</span>
        </button>
        <p class="advanced-edit-info">使用 QGIS 进行高级地理数据编辑</p>
      </div>
    </div>
  </div>

  <!-- 导出面板 -->
  <div v-if="showExportPanel" class="export-overlay" @click="closeExportPanel">
    <div class="export-container" @click.stop>
      <ExportPanel 
        :geoManager="props.geoManager || geoManager"
        :selectedFeatures="selectedFeatures"
        @export-complete="handleExportComplete"
        @close="closeExportPanel"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { GeoManager, type GeoFeature } from '@/utils/geo'
import { geoEventBus, GEO_EVENTS } from '@/utils/geo/core/eventBus'
import ExportPanel from '@/components/Export/ExportPanel.vue'
import { launchQGIS, showQGISInstallGuide } from '@/utils/qgisLauncher'

// Props定义
interface Props {
  geoManager?: GeoManager | null
  viewer?: any // Cesium Viewer
}

const props = withDefaults(defineProps<Props>(), {})
const emit = defineEmits(['featureSelected', 'featureUpdated', 'close'])

// 编辑工具列表
const editTools = [
  { id: 'select', name: '选择', icon: '👆' },
  { id: 'geometry', name: '几何编辑', icon: '✏️' },
  { id: 'split', name: '分割', icon: '✂️' },
  { id: 'merge', name: '合并', icon: '🔗' }
]

// 响应式状态
const activeTool = ref('')
const selectedFeatures = ref<GeoFeature[]>([])
const currentEditingFeature = ref<GeoFeature | null>(null)
const historyList = ref<any[]>([])
const canUndo = ref(false)
const canRedo = ref(false)

// 导出面板状态
const showExportPanel = ref(false)

// 高级编辑状态
const isOpeningQGIS = ref(false)

// 计算属性
const canMerge = computed(() => {
  return selectedFeatures.value.length >= 2 && 
         selectedFeatures.value.every(f => f.type === 'line' || f.type === 'polygon')
})

const hasFeatures = computed(() => {
  const currentGeoManager = props.geoManager || geoManager
  return currentGeoManager ? currentGeoManager.getFeatures().length > 0 : false
})

// GeoManager实例
let geoManager: GeoManager | null = null

// 初始化
onMounted(() => {
  console.log('🚀 GeoEditPanel mounted')
  console.log('🔍 Props.geoManager:', props.geoManager)
  console.log('🔍 Props.viewer:', props.viewer)
  
  if (props.geoManager) {
    geoManager = props.geoManager
  } else if (props.viewer) {
    geoManager = new GeoManager(props.viewer)
  }

  if (geoManager) {
    setupEventListeners()
    updateHistoryState()

    // 加载现有图层数据
    geoManager.loadLayerData()
  }
})

onUnmounted(() => {
  removeEventListeners()
})

// 事件监听器设置
const setupEventListeners = () => {
  geoEventBus.on(GEO_EVENTS.SELECTION_CHANGED, handleSelectionChanged)
  geoEventBus.on(GEO_EVENTS.FEATURE_CREATED, handleFeatureCreated)
  geoEventBus.on(GEO_EVENTS.FEATURE_UPDATED, handleFeatureUpdated)
  geoEventBus.on(GEO_EVENTS.FEATURE_DELETED, handleFeatureDeleted)
  geoEventBus.on(GEO_EVENTS.HISTORY_ADDED, handleHistoryChanged)
  geoEventBus.on(GEO_EVENTS.TOOL_STATE_CHANGED, handleToolChanged)
}

const removeEventListeners = () => {
  geoEventBus.off(GEO_EVENTS.SELECTION_CHANGED, handleSelectionChanged)
  geoEventBus.off(GEO_EVENTS.FEATURE_CREATED, handleFeatureCreated)
  geoEventBus.off(GEO_EVENTS.FEATURE_UPDATED, handleFeatureUpdated)
  geoEventBus.off(GEO_EVENTS.FEATURE_DELETED, handleFeatureDeleted)
  geoEventBus.off(GEO_EVENTS.HISTORY_ADDED, handleHistoryChanged)
  geoEventBus.off(GEO_EVENTS.TOOL_STATE_CHANGED, handleToolChanged)
}

// 事件处理器
const handleSelectionChanged = (features: GeoFeature[]) => {
  console.log('🎯 面板收到选择变化事件:', features)
  console.log('🎯 收到的要素数量:', features.length)
  selectedFeatures.value = features
  if (features.length === 1) {
    currentEditingFeature.value = features[0]
    console.log('🎯 设置当前编辑要素:', features[0])
  } else if (features.length === 0) {
    currentEditingFeature.value = null
    console.log('🎯 清空当前编辑要素')
  }
  emit('featureSelected', features)
}

const handleFeatureCreated = (feature: GeoFeature) => {
  console.log('Feature created:', feature)
  selectedFeatures.value = [feature]
  currentEditingFeature.value = feature
}

const handleFeatureUpdated = (feature: GeoFeature) => {
  console.log('Feature updated:', feature)
  if (currentEditingFeature.value?.id === feature.id) {
    currentEditingFeature.value = feature
  }
  emit('featureUpdated', feature)
}

const handleFeatureDeleted = (featureId: string) => {
  console.log('Feature deleted:', featureId)
  selectedFeatures.value = selectedFeatures.value.filter(f => f.id !== featureId)
  if (currentEditingFeature.value?.id === featureId) {
    currentEditingFeature.value = null
  }
}

const handleHistoryChanged = (history: any) => {
  console.log('📝 History changed event received:', history)
  // 更新历史状态
  updateHistoryState()
}

const handleToolChanged = (toolInfo: any) => {
  console.log('Tool changed:', toolInfo)
  activeTool.value = toolInfo.tool || ''
}

// 工具选择
const selectTool = (toolId: string) => {
  console.log('🔧 Selecting tool:', toolId)
  
  // 优先使用 props 中的 geoManager
  const currentGeoManager = props.geoManager || geoManager
  
  console.log('🔧 Current GeoManager available:', !!currentGeoManager)
  console.log('🔧 Props geoManager:', !!props.geoManager)
  console.log('🔧 Local geoManager:', !!geoManager)
  
  if (!currentGeoManager) {
    console.warn('❌ No GeoManager available for tool selection')
    alert('地理编辑管理器未初始化，请稍后再试或刷新页面')
    return
  }

  // 检查 GeoManager 是否有必需的方法
  if (typeof currentGeoManager.activateSelection !== 'function') {
    console.error('❌ GeoManager.activateSelection 方法不存在')
    alert('地理编辑管理器缺少必需的方法，请刷新页面重试')
    return
  }  // 先停用所有工具
  try {
    if (toolId === 'geometry') {
      // 几何编辑模式：停用选择工具但保留选择状态
      console.log('🔧 切换到几何编辑，当前选择数量（before deactivate）:', selectedFeatures.value.length)
      const currentGeoManager = props.geoManager || geoManager
      if (currentGeoManager) {
        currentGeoManager.deactivateSelection(false) // 不清空选择
      }
      console.log('🔧 选择工具已停用，当前选择数量（after deactivate）:', selectedFeatures.value.length)
    } else {
      // 其他模式：正常停用所有工具
      deactivateAllTools()
    }
  } catch (error) {
    console.warn('停用工具时出错:', error)
  }

  activeTool.value = toolId
  try {
    switch (toolId) {
      case 'select':
        console.log('🎯 正在激活选择工具...')
        currentGeoManager.activateSelection('multiple') // 启用多选模式
        console.log('✅ 选择工具已激活！')
        console.log('💡 使用提示：')
        console.log('   - 单击要素进行选择')
        console.log('   - 拖拽画框进行框选')
        console.log('   - 地图拖拽已暂时禁用，选择完成后请切换到其他工具')
        break
      case 'geometry':
        // 直接从GeoManager获取当前选择的要素，避免异步问题
        const currentSelectedFeatures = currentGeoManager.getSelectedFeatures()
        console.log('🔧 几何编辑 - 从GeoManager获取的选择数量:', currentSelectedFeatures.length)
        console.log('🔧 几何编辑 - 面板中的选择数量:', selectedFeatures.value.length)
        console.log('🔧 选中的要素:', currentSelectedFeatures)
        
        // 更新面板状态，确保同步
        if (currentSelectedFeatures.length > 0 && selectedFeatures.value.length === 0) {
          console.log('🔧 检测到异步问题，手动同步选择状态')
          selectedFeatures.value = currentSelectedFeatures
        }
        
        const featuresToEdit = currentSelectedFeatures.length > 0 ? currentSelectedFeatures : selectedFeatures.value
        
        if (featuresToEdit.length === 1) {
          console.log('🔧 正在启动几何编辑...')
          currentGeoManager.startGeometryEdit(featuresToEdit[0])
          console.log('✅ 几何编辑工具已激活')
        } else if (featuresToEdit.length === 0) {
          console.warn('❌ 没有选择任何要素进行几何编辑')
          alert('请先使用选择工具选择一个要素，然后再进行几何编辑')
          activeTool.value = ''
        } else {
          console.warn('❌ 选择了多个要素，无法进行几何编辑')
          alert('一次只能编辑一个要素的几何，请重新选择')
          activeTool.value = ''
        }
        break
      case 'split':
        console.log('✂️ 正在激活分割工具...')
        currentGeoManager.activateSplitTool()
        console.log('✅ 分割工具已激活')
        break
      case 'merge':
        if (canMerge.value) {
          console.log('🔗 正在激活合并工具...')
          currentGeoManager.activateMergeTool()
          console.log('✅ 合并工具已激活')
        } else {
          console.warn('请选择两个或更多相同类型的要素进行合并')
          alert('请选择两个或更多相同类型的要素进行合并')
          activeTool.value = ''
        }
        break
      default:
        console.warn('Unknown tool:', toolId)
        activeTool.value = ''
    }
  } catch (error) {
    console.error('❌ Error selecting tool:', error)
    alert('工具激活失败：' + error)
    activeTool.value = ''
  }
}

// 要素管理
const setCurrentEditingFeature = (feature: GeoFeature) => {
  currentEditingFeature.value = feature
}

const startGeometryEdit = (feature: GeoFeature) => {
  if (!geoManager) return
  
  console.log('Starting geometry edit for feature:', feature)
  geoManager.startGeometryEdit(feature)
  activeTool.value = 'geometry'
}

const updateFeatureProperty = (key: string, value: any) => {
  if (!currentEditingFeature.value || !geoManager) return

  // 确保properties对象存在
  if (!currentEditingFeature.value.properties) {
    currentEditingFeature.value.properties = {}
  }

  if (key === 'name' || key === 'description') {
    currentEditingFeature.value.properties[key] = value
  }
  
  // 触发更新事件
  console.log('Updating feature property:', key, value)
  geoManager.updateFeature(currentEditingFeature.value)
}

const updateFeatureStyle = () => {
  if (!currentEditingFeature.value || !geoManager) return
  
  console.log('Updating feature style')
  geoManager.updateFeature(currentEditingFeature.value)
}

const saveFeature = () => {
  if (!currentEditingFeature.value || !geoManager) return

  console.log('Saving feature:', currentEditingFeature.value)
  geoManager.updateFeature(currentEditingFeature.value)
}

const deleteFeature = (feature?: GeoFeature) => {
  if (!geoManager) return

  const targetFeature = feature || currentEditingFeature.value
  if (targetFeature) {
    console.log('Deleting feature:', targetFeature)
    geoManager.deleteFeature(targetFeature.id)
  }
}

const cancelEdit = () => {
  currentEditingFeature.value = null
  activeTool.value = ''
  if (geoManager) {
    geoManager.deactivateCurrentTool()
  }
}

// 批量操作
const activateMergeTool = () => {
  if (!geoManager || !canMerge.value) return
  
  console.log('Activating merge tool for multiple features')
  geoManager.activateMergeTool()
  activeTool.value = 'merge'
}

const deleteBatch = () => {
  if (!geoManager) return

  console.log('Batch deleting features:', selectedFeatures.value)
  selectedFeatures.value.forEach(feature => {
    geoManager!.deleteFeature(feature.id)
  })
}

const clearSelection = () => {
  if (!geoManager) return
  
  console.log('Clearing selection')
  geoManager.clearSelection()
}

// 历史操作
const undo = () => {
  const currentGeoManager = props.geoManager || geoManager
  if (!currentGeoManager || !canUndo.value) return
  
  console.log('Undoing last action')
  currentGeoManager.undo()
  updateHistoryState()
}

const redo = () => {
  const currentGeoManager = props.geoManager || geoManager
  if (!currentGeoManager || !canRedo.value) return
  
  console.log('Redoing last action')
  currentGeoManager.redo()
  updateHistoryState()
}

const updateHistoryState = () => {
  const currentGeoManager = props.geoManager || geoManager
  if (!currentGeoManager) {
    console.warn('⚠️ No GeoManager available for history update')
    return
  }
  
  console.log('🔄 Updating history state...')
  const history = currentGeoManager.getHistory()
  historyList.value = history
  canUndo.value = currentGeoManager.canUndo()
  canRedo.value = currentGeoManager.canRedo()
  
  console.log('📊 History state updated:', {
    historyCount: history.length,
    canUndo: canUndo.value,
    canRedo: canRedo.value
  })
}

// 导出相关方法
const closeExportPanel = () => {
  showExportPanel.value = false
}

const handleExportComplete = (exportInfo: any) => {
  console.log('导出完成:', exportInfo)
  showExportPanel.value = false
  
  // 可以在这里显示成功消息
  alert(`导出成功！文件: ${exportInfo.filename}.${exportInfo.format}，包含 ${exportInfo.featuresCount} 个要素`)
}

// 停用所有工具
const deactivateAllTools = () => {
  const currentGeoManager = props.geoManager || geoManager
  if (!currentGeoManager) return
  
  console.log('停用所有编辑工具')
  
  try {
    currentGeoManager.deactivateSelection()
    currentGeoManager.stopEdit()
    currentGeoManager.deactivateSplitTool()
    currentGeoManager.deactivateMergeTool()
    
    activeTool.value = ''
    console.log('✅ 所有工具已停用，地图控制已恢复')
  } catch (error) {
    console.error('停用工具时出错:', error)
  }
}

// 工具函数
const getFeatureTypeName = (type: string): string => {
  const typeMap: Record<string, string> = {
    'point': '点',
    'line': '线',
    'polygon': '面',
    'circle': '圆',
    'rectangle': '矩形'
  }
  return typeMap[type] || type
}

const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleTimeString()
}

// 高级编辑功能
const openQGIS = async () => {
  if (isOpeningQGIS.value) return

  isOpeningQGIS.value = true

  try {
    console.log('🗺️ 尝试启动 QGIS...')

    const result = await launchQGIS()

    if (result.success) {
      console.log('✅ QGIS 启动成功，方法:', result.method)
      alert('QGIS 已成功启动！')
    } else {
      console.warn('⚠️ QGIS 启动失败:', result.error)

      // 根据启动方法提供不同的错误处理
      if (result.method === 'browser-protocol') {
        const userConfirm = confirm(
          `QGIS 启动失败：${result.error}\n\n` +
          '可能的原因：\n' +
          '• QGIS 未安装\n' +
          '• 浏览器不支持协议启动\n' +
          '• 需要手动关联协议\n\n' +
          '是否需要查看安装和配置说明？'
        )

        if (userConfirm) {
          showQGISInstallGuide()
        }
      } else {
        alert(`QGIS 启动失败：${result.error}\n\n请确保已正确安装 QGIS。`)
      }
    }
  } catch (error) {
    console.error('❌ 启动 QGIS 时发生错误:', error)
    alert(`启动 QGIS 时发生错误：${error}\n\n请检查 QGIS 是否正确安装。`)
  } finally {
    isOpeningQGIS.value = false
  }
}
</script>
<style scoped>
.geoedit-panel {
  padding: 1rem;
}

.panel-section {
  margin-bottom: 2rem;
}

.panel-section h3 {
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #333;
}

.tool-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.tool-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tool-button:hover {
  background: #f5f5f5;
}

.tool-button.active {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.tool-button .icon {
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.attribute-form {
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 8px;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #666;
  font-size: 0.75rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 0.75rem;
}

.form-group textarea {
  height: 80px;
  resize: vertical;
}

.button-group {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.save-btn,
.cancel-btn {
  flex: 1;
  padding: 0.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: opacity 0.2s;
  font-size: 0.75rem;
}

.save-btn {
  background: #1890ff;
  color: white;
}

.cancel-btn {
  background: #d9d9d9;
  color: #666;
}

.save-btn:hover,
.cancel-btn:hover {
  opacity: 0.8;
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 0.5rem;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.history-action {
  flex: 1;
  color: #333;
  font-size: 0.75rem;
}

.history-time {
  color: #999;
  margin: 0 1rem;
  font-size: 0.625rem;
}

.no-history {
  text-align: center;
  color: #999;
  padding: 1rem;
  font-size: 0.75rem;
}

/* 选中要素列表样式 */
.selected-features {
  max-height: 200px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 0.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background: white;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  border: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
}

.feature-item:hover {
  background: #f8f9fa;
  border-color: #1890ff;
}

.feature-item.editing {
  background: #e6f7ff;
  border-color: #1890ff;
}

.feature-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.feature-type {
  font-size: 0.75rem;
  color: #1890ff;
  font-weight: 500;
}

.feature-name {
  font-size: 0.75rem;
  color: #333;
}

.feature-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  padding: 0.25rem 0.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.75rem;
}

.edit-btn {
  background: #52c41a;
  color: white;
}

.delete-btn {
  background: #ff4d4f;
  color: white;
}

.action-btn:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

/* 批量操作样式 */
.batch-operations {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.batch-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.batch-btn:hover {
  background: #f5f5f5;
  border-color: #1890ff;
}

.batch-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.batch-btn .icon {
  font-size: 0.875rem;
}

/* 历史控制样式 */
.history-controls {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.history-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.75rem;
}

.history-btn:hover {
  background: #f5f5f5;
  border-color: #1890ff;
}

.history-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f5f5f5;
}

.history-btn .icon {
  font-size: 0.875rem;
}

/* 样式控制区域 */
.style-controls {
  display: flex;
  gap: 1rem;
}

.color-control {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.color-control label {
  font-size: 0.75rem;
  color: #666;
}

.color-control input[type="color"] {
  width: 40px;
  height: 30px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

/* 导出功能样式 */
.export-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.export-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.75rem;
  transition: background-color 0.2s;
}

.export-btn:hover:not(:disabled) {
  background: #218838;
}

.export-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.export-info {
  font-size: 10px;
  color: #666;
  margin: 0;
  text-align: center;
}

.export-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.export-container {
  background: white;
  border-radius: 8px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
}

/* 高级编辑功能样式 */
.advanced-edit-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.advanced-edit-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  background: #722ed1;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.75rem;
  transition: background-color 0.2s;
}

.advanced-edit-btn:hover:not(:disabled) {
  background: #531dab;
}

.advanced-edit-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.advanced-edit-info {
  font-size: 10px;
  color: #666;
  margin: 0;
  text-align: center;
}
</style>
