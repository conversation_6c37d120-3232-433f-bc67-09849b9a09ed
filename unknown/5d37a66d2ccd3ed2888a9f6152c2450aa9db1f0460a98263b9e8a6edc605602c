<template>
  <!-- 侧边栏模式 -->
  <div
    v-if="dialogVisible"
    class="buffer-analysis-sidebar"
    :class="{ 'minimized': isMinimized }"
  >
    <!-- 侧边栏标题栏 -->
    <div class="sidebar-header">
      <span class="sidebar-title">缓冲区分析</span>
      <div class="sidebar-controls">
        <el-button
          :icon="isMinimized ? 'ArrowRight' : 'ArrowLeft'"
          circle
          size="small"
          @click="toggleMinimize"
          :title="isMinimized ? '展开' : '收起'"
        />
        <el-button
          icon="Close"
          circle
          size="small"
          @click="closeDialog"
          title="关闭"
        />
      </div>
    </div>
    <!-- 侧边栏内容 -->
    <div class="sidebar-content" v-show="!isMinimized">
      <!-- 复用现有的BufferAnalysis组件内容 -->
      <buffer-analysis
        ref="bufferAnalysisRef"
        @analyze="handleAnalyze"
        @clear="handleClear"
        @file-loaded="handleFileLoaded"
      />
        <!-- 文件信息显示 -->
      <div v-if="loadedLayers.length > 0" class="file-info-table">
        <h4>数据信息</h4>
        <el-table :data="loadedLayers" size="small" border style="width: 100%">
          <el-table-column prop="name" label="文件名" width="100" />
          <el-table-column prop="type" label="类型" width="60" />
          <el-table-column prop="featureCount" label="数量" width="60" />
          <el-table-column label="操作" width="50">
            <template #default="scope">
              <el-button 
                type="danger" 
                :icon="Delete" 
                circle 
                size="small" 
                @click="removeLayer(scope.row)"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 侧边栏底部操作 -->
      <div class="sidebar-footer">
        <el-button @click="closeDialog" size="small">关闭</el-button>
        <el-button @click="openExportDialog" type="primary" size="small">导出</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import * as Cesium from 'cesium'
import { Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import BufferAnalysis from '../components/BufferAnalysis.vue'
import type { VectorFileInfo } from '../types'
import { saveAs } from 'file-saver';
import tokml from 'tokml';
import shpwrite from 'shp-write';

// 定义事件
const emit = defineEmits<{
  close: []
  analyze: [params: any]
  clear: []
}>()

const dialogVisible = ref(false)
const isMinimized = ref(false)
const fileInfo = ref<any>(null)
const loadedLayers = ref<VectorFileInfo[]>([])

// 图层ID到数据源ID的映射
const layerToDataSourceMap = ref<Map<string, string>>(new Map())

// buffer-analysis 组件引用
const bufferAnalysisRef = ref()

// 切换最小化状态
const toggleMinimize = () => {
  isMinimized.value = !isMinimized.value
}

// 处理分析请求
const handleAnalyze = async (params: any) => {
  emit('analyze', params)
  // 分析完成后不自动关闭对话框，用户可能需要继续进行其他分析
  
  // 延迟一下以确保分析结果已添加到地图
  setTimeout(async () => {
    try {
      const { useMapStore } = await import('../../../../stores/map')
      const mapStore = useMapStore()
      
      if (mapStore.viewer && mapStore.viewer.dataSources.length > 0) {
        // 获取最新添加的数据源（应该是分析结果）并飞行过去
        const dataSource = mapStore.viewer.dataSources.get(mapStore.viewer.dataSources.length - 1)
        // 使用缓慢的飞行动画以便用户观察
        mapStore.viewer.flyTo(dataSource, {
          duration: 2,
          offset: new Cesium.HeadingPitchRange(0, -0.5, 0)
        })
      }
    } catch (error) {
      console.warn('飞行到分析结果位置失败:', error)
    }
  }, 500)
}

// 处理文件加载，更新文件信息
const handleFileLoaded = (data: any, info: VectorFileInfo) => {
  fileInfo.value = info
  
  // 将图层添加到列表中
  loadedLayers.value.push({...info})
  
  // 尝试飞行到数据位置
  flyToData(data)
  
  // 不自动关闭对话框，让用户可以配置缓冲区参数
  ElMessage({
    message: `已加载图层: ${info.name}，请配置缓冲区参数`,
    type: 'success',
    duration: 3000
  })
}

// 删除已加载图层
const removeLayer = async (layer: VectorFileInfo) => {
  try {
    const { useMapStore } = await import('../../../../stores/map')
    const { removeGeoJsonFromMap } = await import('../utils/mapDisplay')
    const mapStore = useMapStore()
    const dataSourceId = layerToDataSourceMap.value.get(layer.name)
    // 从地图中删除图层
    if (dataSourceId && mapStore.viewer) {
      removeGeoJsonFromMap(mapStore.viewer, dataSourceId)
    }
    // 从图层列表中删除
    const index = loadedLayers.value.findIndex(l => l.name === layer.name)
    if (index !== -1) {
      loadedLayers.value.splice(index, 1)
    }
    // 从映射中删除
    layerToDataSourceMap.value.delete(layer.name)
    // 同步清除 FileUploader 图层
    await bufferAnalysisRef.value?.removeLayerFromMap?.()
    ElMessage({
      message: `已删除图层: ${layer.name}`,
      type: 'success',
      duration: 2000
    })
  } catch (error) {
    console.error('删除图层失败:', error)
    ElMessage.error('删除图层失败')
  }
}

// 自动飞行到加载的数据
const flyToData = async (_geojson: any) => {
  try {
    const { useMapStore } = await import('../../../../stores/map')
    const mapStore = useMapStore()
    
    if (mapStore.viewer && mapStore.viewer.dataSources.length > 0) {
      // 获取最后添加的数据源并飞行过去
      const dataSource = mapStore.viewer.dataSources.get(mapStore.viewer.dataSources.length - 1)
      const dataSourceId = dataSource.name
      
      // 记录当前图层与数据源ID的映射关系
      if (fileInfo.value && fileInfo.value.name) {
        layerToDataSourceMap.value.set(fileInfo.value.name, dataSourceId)
      }
      
      // 飞行到数据位置上
      mapStore.viewer.flyTo(dataSource, {
        duration: 2,
        offset: new Cesium.HeadingPitchRange(0, -0.5, 0)
      })
    }
  } catch (error) {
    console.warn('飞行到数据位置失败:', error)
  }
}

// 处理清除请求
const handleClear = async () => {
  fileInfo.value = null
  
  // 清除所有加载的图层
  try {
    const { useMapStore } = await import('../../../../stores/map')
    const { removeGeoJsonFromMap } = await import('../utils/mapDisplay')
    const mapStore = useMapStore()
    
    // 从地图中删除所有图层
    if (mapStore.viewer) {
      for (const [_layerName, dataSourceId] of layerToDataSourceMap.value.entries()) {
        // 使用增强的函数移除图层（包括Primitive和Entity）
        removeGeoJsonFromMap(mapStore.viewer, dataSourceId)
      }
    }
    
    // 清空图层列表
    loadedLayers.value = []
    layerToDataSourceMap.value.clear()
  } catch (error) {
    console.error('清除图层失败:', error)
  }
  
  emit('clear')
}

// 处理关闭
const handleClose = () => {
  closeDialog()
}

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
  emit('close')
}

// 打开对话框
const openDialog = () => {
  dialogVisible.value = true
}

// 导出选项
const exportOptions = ref({ format: 'geojson' });

// 打开导出对话框
const openExportDialog = () => {
  const format = exportOptions.value.format;
  const bufferData = bufferAnalysisRef.value?.getBufferData();

  if (!bufferData) {
    ElMessage.error('没有可导出的缓冲区数据');
    return;
  }

  let blob;
  switch (format) {
    case 'geojson':
      blob = new Blob([JSON.stringify(bufferData)], { type: 'application/json' });
      saveAs(blob, 'buffer_result.geojson');
      break;
    case 'kml':
      const kml = tokml(bufferData);
      blob = new Blob([kml], { type: 'application/vnd.google-earth.kml+xml' });
      saveAs(blob, 'buffer_result.kml');
      break;
    case 'shapefile':
      shpwrite.download(bufferData, { file: 'buffer_result' });
      break;
    default:
      ElMessage.error('不支持的导出格式');
  }
};

// 向父组件暴露方法
defineExpose({
  openDialog,
  closeDialog
})
</script>

<style scoped>
/* 侧边栏样式 */
.buffer-analysis-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  z-index: 2000;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
}

.buffer-analysis-sidebar.minimized {
  transform: translateX(350px);
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.sidebar-title {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
}

.sidebar-controls {
  display: flex;
  gap: 8px;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.sidebar-footer {
  padding: 12px 16px;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  flex-shrink: 0;
}

/* 文件信息表格样式 */
.file-info-table {
  margin-top: 1rem;
  border-top: 1px solid #eaeaea;
  padding-top: 1rem;
}

.file-info-table h4 {
  font-size: 12px;
  margin-bottom: 0.5rem;
  color: #606266;
}

/* 侧边栏内部组件样式调整 */
:deep(.buffer-analysis) {
  padding: 0;
}

:deep(.el-table) {
  font-size: 10px;
}

:deep(.el-form-item) {
  margin-bottom: 12px;
}

:deep(.el-button--small) {
  padding: 6px 12px;
  font-size: 10px;
}

:deep(.el-button.is-circle) {
  padding: 6px;
}

/* 确保侧边栏控制按钮样式 */
.sidebar-controls .el-button {
  width: 28px;
  height: 28px;
  padding: 0;
}
</style>
