export declare enum DatabaseType {
    POSTGRESQL = "postgresql",
    MYSQL = "mysql",
    SQLITE = "sqlite"
}
export declare class CreateDatabaseConnectionDto {
    name: string;
    type: DatabaseType;
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    schema?: string;
}
export declare class TestConnectionDto {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    schema?: string;
}
export declare class QueryLayerDto {
    connectionId: string;
    tableName: string;
    geometryColumn?: string;
    whereClause?: string;
    limit?: number;
}
export declare class DatabaseTableInfo {
    name: string;
    schema: string;
    type: 'table' | 'view';
    geometryColumns: GeometryColumnInfo[];
    totalRows?: number;
}
export declare class GeometryColumnInfo {
    columnName: string;
    geometryType: string;
    srid: number;
    dimension: number;
}
export declare class ConnectionTestResult {
    success: boolean;
    message: string;
    version?: string;
    postgisVersion?: string;
}
export declare class LayerQueryResult {
    success: boolean;
    data?: any;
    totalFeatures?: number;
    bounds?: [number, number, number, number];
    error?: string;
}
