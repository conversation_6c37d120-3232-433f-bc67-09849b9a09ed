import { JwtService } from '@nestjs/jwt';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { UserSession } from '../entities/user-session.entity';
import { AuditLog } from '../entities/audit-log.entity';
import { LoginDto, ChangePasswordDto, RegisterDto, EmailLoginDto } from './dto/login.dto';
import { Role } from '../entities/role.entity';
import { JwtPayload } from './strategies/jwt.strategy';
import { MailService } from '../mail/mail.service';
export interface LoginResponse {
    accessToken: string;
    refreshToken: string;
    user: {
        id: string;
        username: string;
        email: string;
        fullName: string;
        roles: string[];
        permissions: string[];
        isActive: boolean;
    };
    expiresIn: number;
}
export interface RefreshTokenResponse {
    accessToken: string;
    expiresIn: number;
}
export declare class AuthService {
    private readonly userRepository;
    private readonly sessionRepository;
    private readonly auditRepository;
    private readonly roleRepository;
    private readonly jwtService;
    private readonly mailService;
    private readonly logger;
    constructor(userRepository: Repository<User>, sessionRepository: Repository<UserSession>, auditRepository: Repository<AuditLog>, roleRepository: Repository<Role>, jwtService: JwtService, mailService: MailService);
    validateUser(username: string, password: string): Promise<User | null>;
    login(loginDto: LoginDto, userAgent?: string, ipAddress?: string): Promise<LoginResponse>;
    refreshToken(refreshToken: string): Promise<RefreshTokenResponse>;
    logout(refreshToken: string): Promise<void>;
    logoutAllDevices(userId: string): Promise<void>;
    changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<void>;
    resetPassword(username: string, newPassword: string): Promise<{
        message: string;
    }>;
    validateJwtPayload(payload: JwtPayload): Promise<User>;
    private generateTokens;
    private generateAccessToken;
    private createUserSession;
    private handleFailedLogin;
    private getUserPermissions;
    private logSecurityEvent;
    register(registerDto: RegisterDto): Promise<{
        message: string;
        user: {
            id: string;
            username: string;
            email: string;
            fullName: string;
        };
    }>;
    sendEmailCode(email: string, type?: 'login' | 'register' | 'reset'): Promise<{
        message: string;
    }>;
    verifyEmailCode(email: string, code: string): Promise<boolean>;
    loginWithEmail(emailLoginDto: EmailLoginDto): Promise<LoginResponse>;
}
